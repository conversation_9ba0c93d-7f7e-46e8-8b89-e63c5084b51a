import React from 'react';
import { Link } from '@/i18n/routing';
import { getTranslations } from 'next-intl/server';
import LikeButton from '@/components/tools/LikeButton';
import CommentSectionServer from '@/components/tools/CommentSectionServer';
import ToolDetailInteraction from '@/components/tools/ToolDetailInteraction';
import { apiClient, Tool } from '@/lib/api';
import { getToolPricingColor, getToolPricingText } from '@/constants/pricing';
import {
  ExternalLink,
  Heart,
  Eye,
  Tag,
  DollarSign,
  Share2
} from 'lucide-react';
import { Locale } from '@/i18n/config';

interface ToolDetailServerProps {
  initialTool: Tool;
  toolId: string;
  locale: Locale;
}

// 获取相关工具
async function getRelatedTools(category: string, currentToolId: string): Promise<Tool[]> {
  try {
    const response = await apiClient.getTools({
      category,
      limit: 4,
      status: 'approved'
    });
    
    if (response.success && response.data) {
      // 过滤掉当前工具
      return response.data.tools.filter(tool => tool._id !== currentToolId);
    }
    return [];
  } catch (error) {
    console.error('Error fetching related tools:', error);
    return [];
  }
}

export default async function ToolDetailServer({ 
  initialTool, 
  toolId, 
  locale 
}: ToolDetailServerProps) {
  const t = await getTranslations({ locale, namespace: 'tool_detail' });
  const relatedTools = await getRelatedTools(initialTool.category, toolId);

  return (
    <ToolDetailInteraction>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2">
          {/* Tool Header */}
          <article className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <header className="flex items-start justify-between mb-6">
              <div className="flex items-center space-x-4">
                {initialTool.logo ? (
                  <img
                    src={initialTool.logo}
                    alt={`${initialTool.name} logo`}
                    className="w-16 h-16 rounded-lg object-cover"
                  />
                ) : (
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-2xl">
                      {initialTool.name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                )}
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 mb-2">
                    {initialTool.name}
                  </h1>
                  {initialTool.tagline && (
                    <p className="text-lg text-gray-600 mb-2">
                      {initialTool.tagline}
                    </p>
                  )}
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getToolPricingColor(initialTool.pricing)}`}>
                    {getToolPricingText(initialTool.pricing)}
                  </span>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <LikeButton
                  toolId={toolId}
                  size="lg"
                />
                <a
                  href={initialTool.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <ExternalLink className="mr-2 h-4 w-4" />
                  {t('visit_website')}
                </a>
              </div>
            </header>

            {/* Tool Description */}
            <div className="prose max-w-none">
              <h2 className="text-xl font-semibold text-gray-900 mb-3">
                {t('description')}
              </h2>
              <p className="text-gray-700 leading-relaxed">
                {initialTool.description}
              </p>
            </div>

            {/* Tool Tags */}
            {initialTool.tags && initialTool.tags.length > 0 && (
              <div className="mt-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <Tag className="mr-2 h-5 w-5" />
                  {t('tags')}
                </h3>
                <div className="flex flex-wrap gap-2">
                  {initialTool.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-800"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Tool Stats */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="flex items-center space-x-6">
                <div className="flex items-center text-gray-600">
                  <Eye className="mr-2 h-5 w-5" />
                  <span>{initialTool.views || 0} {t('views')}</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <Heart className="mr-2 h-5 w-5" />
                  <span>{initialTool.likes || 0} {t('likes')}</span>
                </div>
              </div>
            </div>
          </article>

          {/* Comments Section */}
          <CommentSectionServer toolId={toolId} />
        </div>

        {/* Sidebar */}
        <aside className="lg:col-span-1">
          {/* Tool Info */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('tool_info')}</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">{t('category')}</span>
                <span className="text-gray-900 font-medium">{initialTool.category}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">{t('pricing_model')}</span>
                <span className={`px-2 py-1 rounded text-sm font-medium ${getToolPricingColor(initialTool.pricing)}`}>
                  {getToolPricingText(initialTool.pricing)}
                </span>
              </div>
              {initialTool.launchDate && (
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">{t('launch_date')}</span>
                  <span className="text-gray-900 font-medium">
                    {new Date(initialTool.launchDate).toLocaleDateString()}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Related Tools */}
          {relatedTools.length > 0 && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('related_tools')}</h3>
              <div className="space-y-4">
                {relatedTools.map((tool) => (
                  <Link
                    key={tool._id}
                    href={`/tools/${tool._id}`}
                    className="block p-3 rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-sm transition-all"
                  >
                    <div className="flex items-center space-x-3">
                      {tool.logo ? (
                        <img
                          src={tool.logo}
                          alt={`${tool.name} logo`}
                          className="w-10 h-10 rounded object-cover"
                        />
                      ) : (
                        <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded flex items-center justify-center">
                          <span className="text-white font-bold text-sm">
                            {tool.name.charAt(0).toUpperCase()}
                          </span>
                        </div>
                      )}
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium text-gray-900 truncate">
                          {tool.name}
                        </h4>
                        <p className="text-xs text-gray-500 truncate">
                          {tool.description}
                        </p>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          )}
        </aside>
      </div>
    </ToolDetailInteraction>
  );
}
