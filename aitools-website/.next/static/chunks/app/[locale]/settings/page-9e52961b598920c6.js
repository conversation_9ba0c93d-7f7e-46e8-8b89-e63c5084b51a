(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2455],{981:(e,t,r)=>{"use strict";r.d(t,{A:()=>x});var a=r(5695),s=r(2115),l=r.t(s,2),i=r(3385),n=l["use".trim()],c=r(3225),o=r(6160),d=r(469),m=r(5155),u=r(8986);function x(e){let{Link:t,config:r,getPathname:l,...x}=function(e,t){var r,l,i;let u={...r=t||{},localePrefix:"object"==typeof(i=r.localePrefix)?i:{mode:i||"always"},localeCookie:!!((l=r.localeCookie)??1)&&{name:"NEXT_LOCALE",sameSite:"lax",..."object"==typeof l&&l},localeDetection:r.localeDetection??!0,alternateLinks:r.alternateLinks??!0},x=u.pathnames,f=(0,s.forwardRef)(function({href:t,locale:r,...a},s){let l,i;"object"==typeof t?(l=t.pathname,i=t.params):l=t;let d=(0,c._x)(t),f=e(),p=(0,c.yL)(f)?n(f):f,b=d?h({locale:r||p,href:null==x?l:{pathname:l,params:i},forcePrefix:null!=r||void 0}):l;return(0,m.jsx)(o.default,{ref:s,href:"object"==typeof t?{...t,pathname:b}:b,locale:r,localeCookie:u.localeCookie,...a})});function h(e){let t,{forcePrefix:r,href:a,locale:s}=e;return null==x?"object"==typeof a?(t=a.pathname,a.query&&(t+=(0,d.Zn)(a.query))):t=a:t=(0,d.FP)({locale:s,...(0,d.TK)(a),pathnames:u.pathnames}),(0,d.x3)(t,s,u,r)}function p(e){return function(t,...r){return e(h(t),...r)}}return{config:u,Link:f,redirect:p(a.redirect),permanentRedirect:p(a.permanentRedirect),getPathname:h}}(i.Ym,e);return{...x,Link:t,usePathname:function(){let e=function(e){let t=(0,a.usePathname)(),r=(0,i.Ym)();return(0,s.useMemo)(()=>{if(!t)return t;let a=t,s=(0,c.XP)(r,e.localePrefix);if((0,c.wO)(s,t))a=(0,c.MY)(t,s);else if("as-needed"===e.localePrefix.mode&&e.localePrefix.prefixes){let e=(0,c.bL)(r);(0,c.wO)(e,t)&&(a=(0,c.MY)(t,e))}return a},[e.localePrefix,r,t])}(r),t=(0,i.Ym)();return(0,s.useMemo)(()=>e&&r.pathnames?(0,d.aM)(t,e,r.pathnames):e,[t,e])},useRouter:function(){let e=(0,a.useRouter)(),t=(0,i.Ym)(),n=(0,a.usePathname)();return(0,s.useMemo)(()=>{function a(e){return function(a,s){let{locale:i,...c}=s||{},o=[l({href:a,locale:i||t})];Object.keys(c).length>0&&o.push(c),e(...o),(0,u.A)(r.localeCookie,n,t,i)}}return{...e,push:a(e.push),replace:a(e.replace),prefetch:a(e.prefetch)}},[t,n,e])},getPathname:l}}},1007:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1094:(e,t,r)=>{Promise.resolve().then(r.bind(r,2724))},2388:(e,t,r)=>{"use strict";r.d(t,{N_:()=>i,a8:()=>c,rd:()=>o});var a=r(9984),s=r(981);let l=(0,a.A)({locales:["en","zh"],defaultLocale:"en",localePrefix:"always",localeDetection:!0,alternateLinks:!0,localeCookie:{name:"NEXT_LOCALE",sameSite:"lax",maxAge:31536e3}}),{Link:i,redirect:n,usePathname:c,useRouter:o}=(0,s.A)(l)},2724:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var a=r(5155),s=r(2115),l=r(2108),i=r(2388),n=r(2731),c=r(9783),o=r(7550),d=r(1007),m=r(9946);let u=(0,m.A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]),x=(0,m.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),f=(0,m.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),h=(0,m.A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]);var p=r(5525);function b(){var e,t,r;let{data:m,status:b,update:g}=(0,l.useSession)(),y=(0,i.rd)(),[j,N]=(0,s.useState)(!1),[v,w]=(0,s.useState)(""),[k,A]=(0,s.useState)(""),[C,P]=(0,s.useState)({name:"",email:"",bio:"",website:"",location:""}),[L,M]=(0,s.useState)({emailNotifications:!0,toolApprovalNotifications:!0,weeklyDigest:!1,marketingEmails:!1}),[E,S]=(0,s.useState)({profileVisibility:"public",showEmail:!1,showSubmittedTools:!0});(0,s.useEffect)(()=>{if("unauthenticated"===b)return void y.push("/");"authenticated"===b&&(null==m?void 0:m.user)&&P({name:m.user.name||"",email:m.user.email||"",bio:"",website:"",location:""})},[b,m,y]);let _=async e=>{e.preventDefault(),N(!0),w(""),A("");try{await new Promise(e=>setTimeout(e,1e3)),await g({...m,user:{...null==m?void 0:m.user,name:C.name,email:C.email}}),A("个人资料已更新")}catch(e){w("更新失败，请重试")}finally{N(!1)}},z=async e=>{e.preventDefault(),N(!0),w(""),A("");try{await new Promise(e=>setTimeout(e,1e3)),A("通知设置已更新")}catch(e){w("更新失败，请重试")}finally{N(!1)}},D=async e=>{e.preventDefault(),N(!0),w(""),A("");try{await new Promise(e=>setTimeout(e,1e3)),A("隐私设置已更新")}catch(e){w("更新失败，请重试")}finally{N(!1)}};return"loading"===b?(0,a.jsx)(Layout,{children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsx)(n.A,{size:"lg",className:"py-20"})})}):m?(0,a.jsx)(Layout,{children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"flex items-center mb-8",children:[(0,a.jsx)(i.N_,{href:"/profile",className:"mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(o.A,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"账户设置"}),(0,a.jsx)("p",{className:"text-lg text-gray-600",children:"管理您的个人资料和偏好设置"})]})]}),k&&(0,a.jsx)("div",{className:"mb-6 p-4 bg-green-50 border border-green-200 rounded-lg",children:(0,a.jsx)("p",{className:"text-green-800",children:k})}),v&&(0,a.jsx)(c.default,{message:v,onClose:()=>w(""),className:"mb-6"}),(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsx)(d.A,{className:"h-6 w-6 text-gray-600 mr-3"}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"个人资料"})]}),(0,a.jsxs)("form",{onSubmit:_,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"头像"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-16 h-16 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden",children:(null==(e=m.user)?void 0:e.image)?(0,a.jsx)("img",{src:m.user.image,alt:m.user.name||"",className:"w-full h-full object-cover"}):(0,a.jsx)("span",{className:"text-xl font-medium text-gray-600",children:(null==(r=m.user)||null==(t=r.name)?void 0:t.charAt(0))||"U"})}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("button",{type:"button",className:"inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors",children:[(0,a.jsx)(u,{className:"mr-2 h-4 w-4"}),"更换头像"]}),(0,a.jsxs)("button",{type:"button",className:"inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-lg text-red-700 bg-white hover:bg-red-50 transition-colors",children:[(0,a.jsx)(x,{className:"mr-2 h-4 w-4"}),"删除"]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"姓名"}),(0,a.jsx)("input",{type:"text",id:"name",value:C.name,onChange:e=>P(t=>({...t,name:e.target.value})),className:"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱"}),(0,a.jsx)("input",{type:"email",id:"email",value:C.email,onChange:e=>P(t=>({...t,email:e.target.value})),className:"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"bio",className:"block text-sm font-medium text-gray-700 mb-2",children:"个人简介"}),(0,a.jsx)("textarea",{id:"bio",rows:3,value:C.bio,onChange:e=>P(t=>({...t,bio:e.target.value})),placeholder:"介绍一下您自己...",className:"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"website",className:"block text-sm font-medium text-gray-700 mb-2",children:"个人网站"}),(0,a.jsx)("input",{type:"url",id:"website",value:C.website,onChange:e=>P(t=>({...t,website:e.target.value})),placeholder:"https://example.com",className:"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"location",className:"block text-sm font-medium text-gray-700 mb-2",children:"所在地"}),(0,a.jsx)("input",{type:"text",id:"location",value:C.location,onChange:e=>P(t=>({...t,location:e.target.value})),placeholder:"城市, 国家",className:"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsxs)("button",{type:"submit",disabled:j,className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 transition-colors",children:[j?(0,a.jsx)(n.A,{size:"sm",className:"mr-2"}):(0,a.jsx)(f,{className:"mr-2 h-5 w-5"}),"保存更改"]})})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsx)(h,{className:"h-6 w-6 text-gray-600 mr-3"}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"通知设置"})]}),(0,a.jsxs)("form",{onSubmit:z,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"邮件通知"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"接收重要更新的邮件通知"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:L.emailNotifications,onChange:e=>M(t=>({...t,emailNotifications:e.target.checked})),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"工具审核通知"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"当您提交的工具审核状态变更时通知您"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:L.toolApprovalNotifications,onChange:e=>M(t=>({...t,toolApprovalNotifications:e.target.checked})),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"每周摘要"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"接收每周的新工具和热门内容摘要"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:L.weeklyDigest,onChange:e=>M(t=>({...t,weeklyDigest:e.target.checked})),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"营销邮件"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"接收产品更新和特别优惠信息"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:L.marketingEmails,onChange:e=>M(t=>({...t,marketingEmails:e.target.checked})),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]})]}),(0,a.jsx)("div",{className:"flex justify-end pt-4",children:(0,a.jsxs)("button",{type:"submit",disabled:j,className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 transition-colors",children:[j?(0,a.jsx)(n.A,{size:"sm",className:"mr-2"}):(0,a.jsx)(f,{className:"mr-2 h-5 w-5"}),"保存设置"]})})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsx)(p.A,{className:"h-6 w-6 text-gray-600 mr-3"}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"隐私设置"})]}),(0,a.jsxs)("form",{onSubmit:D,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"个人资料可见性"}),(0,a.jsxs)("select",{value:E.profileVisibility,onChange:e=>S(t=>({...t,profileVisibility:e.target.value})),className:"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"public",children:"公开 - 任何人都可以查看"}),(0,a.jsx)("option",{value:"private",children:"私密 - 只有您可以查看"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"显示邮箱地址"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"在您的公开资料中显示邮箱地址"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:E.showEmail,onChange:e=>S(t=>({...t,showEmail:e.target.checked})),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"显示提交的工具"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"在您的公开资料中显示您提交的工具"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:E.showSubmittedTools,onChange:e=>S(t=>({...t,showSubmittedTools:e.target.checked})),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]})]}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsxs)("button",{type:"submit",disabled:j,className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 transition-colors",children:[j?(0,a.jsx)(n.A,{size:"sm",className:"mr-2"}):(0,a.jsx)(f,{className:"mr-2 h-5 w-5"}),"保存设置"]})})]})]})]})]})}):null}},2731:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(5155);function s(e){let{size:t="md",className:r=""}=e;return(0,a.jsx)("div",{className:"flex justify-center items-center ".concat(r),children:(0,a.jsx)("div",{className:"".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[t]," border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin")})})}},4416:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5339:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5525:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},7550:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},9783:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var a=r(5155),s=r(5339),l=r(4416);function i(e){let{message:t,onClose:r,className:i=""}=e;return(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(i),children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(s.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("p",{className:"text-red-800 text-sm",children:t})}),r&&(0,a.jsx)("button",{onClick:r,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,a.jsx)(l.A,{className:"w-4 h-4"})})]})})}},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var a=r(2115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:s=24,strokeWidth:l=2,absoluteStrokeWidth:i,className:d="",children:m,iconNode:u,...x}=e;return(0,a.createElement)("svg",{ref:t,...o,width:s,height:s,stroke:r,strokeWidth:i?24*Number(l)/Number(s):l,className:n("lucide",d),...!m&&!c(x)&&{"aria-hidden":"true"},...x},[...u.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(m)?m:[m]])}),m=(e,t)=>{let r=(0,a.forwardRef)((r,l)=>{let{className:c,...o}=r;return(0,a.createElement)(d,{ref:l,iconNode:t,className:n("lucide-".concat(s(i(e))),"lucide-".concat(e),c),...o})});return r.displayName=i(e),r}},9984:(e,t,r)=>{"use strict";function a(e){return e}r.d(t,{A:()=>a})}},e=>{var t=t=>e(e.s=t);e.O(0,[3385,6160,2108,8441,1684,7358],()=>t(1094)),_N_E=e.O()}]);