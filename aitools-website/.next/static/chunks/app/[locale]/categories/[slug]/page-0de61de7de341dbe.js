(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[14],{681:(e,t,r)=>{"use strict";r.d(t,{default:()=>m});var s=r(5155),a=r(2115),l=r(7652),d=r(9824),i=r(6932),c=r(6474),n=r(4653),o=r(5968);function u(e){let{tools:t,onFilteredToolsChange:r}=e,[d,u]=(0,a.useState)(""),[m,h]=(0,a.useState)(""),[x,g]=(0,a.useState)("popular"),[p,b]=(0,a.useState)("grid"),[v,y]=(0,a.useState)(!1),f=(0,l.c3)("category_page"),j=[{value:"",label:f("pricing_all")},{value:"free",label:f("pricing_free")},{value:"freemium",label:f("pricing_freemium")},{value:"paid",label:f("pricing_paid")}],w=[{value:"popular",label:f("sort_popular")},{value:"newest",label:f("sort_newest")},{value:"name",label:f("sort_name")},{value:"views",label:f("sort_views")}];return a.useEffect(()=>{r([...t.filter(e=>{let t=e.name.toLowerCase().includes(d.toLowerCase())||e.description.toLowerCase().includes(d.toLowerCase())||e.tags.some(e=>e.toLowerCase().includes(d.toLowerCase())),r=!m||e.pricing===m;return t&&r})].sort((e,t)=>{switch(x){case"popular":return(t.likes||0)-(e.likes||0);case"views":return(t.views||0)-(e.views||0);case"name":return e.name.localeCompare(t.name);case"newest":if(e.createdAt&&t.createdAt)return new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime();return 0;default:return 0}}),p,d)},[t,d,m,x,p,r]),(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:[(0,s.jsxs)("div",{className:"relative mb-4",children:[(0,s.jsx)("input",{type:"text",placeholder:f("search_placeholder"),value:d,onChange:e=>u(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,s.jsx)(i.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"})]}),(0,s.jsx)("div",{className:"md:hidden mb-4",children:(0,s.jsxs)("button",{onClick:()=>y(!v),className:"flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50",children:[(0,s.jsx)(i.A,{className:"mr-2 h-4 w-4"}),f("filter_options"),(0,s.jsx)(c.A,{className:"ml-2 h-4 w-4 transform ".concat(v?"rotate-180":"")})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 ".concat(v?"block":"hidden md:grid"),children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:f("pricing")}),(0,s.jsx)("select",{value:m,onChange:e=>h(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:j.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:f("sort")}),(0,s.jsx)("select",{value:x,onChange:e=>g(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:w.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:f("view")}),(0,s.jsxs)("div",{className:"flex rounded-lg border border-gray-300",children:[(0,s.jsx)("button",{onClick:()=>b("grid"),className:"flex-1 px-3 py-2 text-sm font-medium rounded-l-lg ".concat("grid"===p?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"),children:(0,s.jsx)(n.A,{className:"h-4 w-4 mx-auto"})}),(0,s.jsx)("button",{onClick:()=>b("list"),className:"flex-1 px-3 py-2 text-sm font-medium rounded-r-lg ".concat("list"===p?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"),children:(0,s.jsx)(o.A,{className:"h-4 w-4 mx-auto"})})]})]})]})]})})}function m(e){let{tools:t}=e,[r,c]=(0,a.useState)(t),[n,o]=(0,a.useState)("grid"),[m,h]=(0,a.useState)(""),x=(0,l.c3)("category_page"),g=(0,a.useCallback)((e,t,r)=>{c(e),o(t),h(r)},[]);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(u,{tools:t,onFilteredToolsChange:g}),(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsxs)("p",{className:"text-gray-600",children:[x("results_count",{count:r.length}),m&&" ".concat(x("search_for",{term:m}))]})}),r.length>0?(0,s.jsx)("div",{className:"grid"===n?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:r.map(e=>(0,s.jsx)(d.A,{tool:e},e._id))}):(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"text-gray-400 mb-4",children:(0,s.jsx)(i.A,{className:"h-12 w-12 mx-auto"})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:x("no_results_title")}),(0,s.jsx)("p",{className:"text-gray-600",children:x("no_results_desc")})]})]})}},4416:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4653:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},5339:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5968:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},6096:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var s=r(3385),a=r(5155);function l(e){let{locale:t,...r}=e;if(!t)throw Error(void 0);return(0,a.jsx)(s.Dk,{locale:t,...r})}},6474:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},6932:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},7361:(e,t,r)=>{Promise.resolve().then(r.bind(r,6160)),Promise.resolve().then(r.bind(r,6096)),Promise.resolve().then(r.bind(r,681)),Promise.resolve().then(r.bind(r,9783))},9783:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var s=r(5155),a=r(5339),l=r(4416);function d(e){let{message:t,onClose:r,className:d=""}=e;return(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(d),children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)(a.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)("p",{className:"text-red-800 text-sm",children:t})}),r&&(0,s.jsx)("button",{onClick:r,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,s.jsx)(l.A,{className:"w-4 h-4"})})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,3385,6160,2108,8003,404,9824,8441,1684,7358],()=>t(7361)),_N_E=e.O()}]);