(()=>{var e={};e.id=5534,e.ids=[5534],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10806:(e,t,r)=>{"use strict";function s(){if(process.env.NEXT_PUBLIC_APP_URL)return process.env.NEXT_PUBLIC_APP_URL;{if(process.env.VERCEL_URL)return`https://${process.env.VERCEL_URL}`;if(process.env.NETLIFY&&process.env.URL)return process.env.URL;if(process.env.RAILWAY_STATIC_URL)return process.env.RAILWAY_STATIC_URL;if(process.env.APP_URL)return process.env.APP_URL;let e=process.env.PORT||"3001";return`http://localhost:${e}`}}function o(){if(process.env.NEXT_PUBLIC_API_BASE_URL)return process.env.NEXT_PUBLIC_API_BASE_URL;let e=s();return`${e}/api`}function a(){return"production"}function i(){return"development"===a()}r.d(t,{u:()=>d});let n={baseUrl:s(),apiBaseUrl:o(),nextAuthUrl:process.env.NEXTAUTH_URL?process.env.NEXTAUTH_URL:s(),environment:a(),isDevelopment:i(),isProduction:"production"===a(),port:process.env.PORT||"3001"};i()&&(console.log("\uD83D\uDD27 Dynamic Environment Configuration:"),console.log("  Base URL:",n.baseUrl),console.log("  API Base URL:",n.apiBaseUrl),console.log("  NextAuth URL:",n.nextAuthUrl),console.log("  Environment:",n.environment),console.log("  Port:",n.port));let l=o();class c{constructor(e=l){this.baseURL=e}async request(e,t={}){try{let r=`${this.baseURL}${e}`,s={headers:{"Content-Type":"application/json",...t.headers},...t},o=await fetch(r,s),a=await o.json();if(!o.ok)throw Error(a.error||`HTTP error! status: ${o.status}`);return a}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,r])=>{void 0!==r&&t.append(e,r.toString())});let r=t.toString();return this.request(`/tools${r?`?${r}`:""}`)}async getTool(e){return this.request(`/tools/${e}`)}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,t){return this.request(`/tools/${e}`,{method:"PUT",body:JSON.stringify(t)})}async deleteTool(e){return this.request(`/tools/${e}`,{method:"DELETE"})}async getLikedTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,r])=>{void 0!==r&&t.append(e,r.toString())});let r=t.toString();return this.request(`/user/liked-tools${r?`?${r}`:""}`)}async getAdminTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,r])=>{void 0!==r&&t.append(e,r.toString())});let r=t.toString();return this.request(`/admin/tools${r?`?${r}`:""}`)}async approveTool(e,t){return this.request(`/admin/tools/${e}/approve`,{method:"POST",body:JSON.stringify(t)})}async rejectTool(e,t){return this.request(`/admin/tools/${e}/reject`,{method:"POST",body:JSON.stringify(t)})}async getAdminStats(e){let t=e?`?timeRange=${e}`:"";return this.request(`/admin/stats${t}`)}async getCategories(){return this.request("/categories")}async getOrder(e){return this.request(`/orders/${e}`)}async processOrderPayment(e,t){return this.request(`/orders/${e}/pay`,{method:"POST",body:JSON.stringify(t)})}}let d=new c},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11011:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var s=r(60687),o=r(93613),a=r(11860);function i({message:e,onClose:t,className:r=""}){return(0,s.jsx)("div",{className:`bg-red-50 border border-red-200 rounded-lg p-4 ${r}`,children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)(o.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)("p",{className:"text-red-800 text-sm",children:e})}),t&&(0,s.jsx)("button",{onClick:t,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,s.jsx)(a.A,{className:"w-4 h-4"})})]})})}},11860:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19995:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f,generateMetadata:()=>h});var s=r(37413);r(61120);var o=r(64348),a=r(78878),i=r(42964),n=r(56333),l=r(26373);let c=(0,l.A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]),d=(0,l.A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);async function u({categories:e,error:t,locale:r}){let l=await (0,o.A)({locale:r,namespace:"categories_page"});if(t)return(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,s.jsx)(n.default,{message:t})});let u=e.sort((e,t)=>t.toolCount-e.toolCount).slice(0,6);return(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsxs)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:[(0,s.jsx)(c,{className:"inline-block mr-3 h-10 w-10 text-blue-600"}),l("title")]}),(0,s.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:l("subtitle")})]}),(0,s.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-100 rounded-lg p-8 mb-12",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 text-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:e.length}),(0,s.jsx)("div",{className:"text-gray-700",children:l("categories_count")})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:e.reduce((e,t)=>e+t.toolCount,0)}),(0,s.jsx)("div",{className:"text-gray-700",children:l("tools_count")})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:e.length>0?Math.round(e.reduce((e,t)=>e+t.toolCount,0)/e.length):0}),(0,s.jsx)("div",{className:"text-gray-700",children:l("avg_tools_per_category")})]})]})}),(0,s.jsxs)("section",{className:"mb-16",children:[(0,s.jsxs)("div",{className:"flex items-center mb-8",children:[(0,s.jsx)(d,{className:"h-8 w-8 text-blue-600 mr-3"}),(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900",children:l("popular_categories")})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:u.map(e=>(0,s.jsx)(i.default,{category:e},e._id))})]}),(0,s.jsxs)("section",{children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900",children:l("all_categories")}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:l("categories_total",{count:e.length})})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:e.map(e=>(0,s.jsx)(i.default,{category:e},e._id))})]}),(0,s.jsxs)("section",{className:"mt-16 bg-blue-600 rounded-lg p-8 text-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-white mb-4",children:l("not_found_title")}),(0,s.jsx)("p",{className:"text-blue-100 mb-6",children:l("not_found_desc")}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsx)(a.N_,{href:"/submit",className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-blue-600 bg-white hover:bg-gray-50 transition-colors",children:l("submit_tool")}),(0,s.jsx)(a.N_,{href:"/contact",className:"inline-flex items-center px-6 py-3 border border-white text-base font-medium rounded-lg text-white hover:bg-blue-700 transition-colors",children:l("contact_us")})]})]})]})}var m=r(60366),p=r(10806),g=r(54290);async function h({params:e}){let{locale:t}=await e,r=await (0,o.A)({locale:t,namespace:"categories"});try{let e=await p.u.getCategories(),s=e.success&&e.data?e.data.categories.length:0,o=e.success&&e.data?e.data.overview.totalTools:0,a=r("page_title"),i=r("page_description",{categories:s,tools:o}),n=r("page_keywords");return{title:a,description:i,keywords:n,authors:[{name:"zh"===t?"AI工具导航团队":"AI Tools Directory Team"}],robots:{index:!0,follow:!0},openGraph:{type:"website",locale:"zh"===t?"zh_CN":"en_US",url:`${process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com"}/${t}/categories`,siteName:r("site_name"),title:a,description:i,images:[{url:"/og-categories.jpg",width:1200,height:630,alt:a}]},twitter:{card:"summary_large_image",title:a,description:i,images:["/og-categories.jpg"]},alternates:{canonical:`${process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com"}/${t}/categories`}}}catch(e){return{title:"zh"===t?"AI工具分类 - 按功能浏览人工智能工具":"AI Tools Categories - Browse AI Tools by Function",description:"zh"===t?"按功能分类浏览AI工具，包括文本生成、图像创作、数据分析、自动化、音频处理等各类人工智能工具分类。":"Browse AI tools by function categories, including text generation, image creation, data analysis, automation, audio processing and other AI tool categories.",keywords:"zh"===t?"AI工具分类,人工智能分类,AI工具类别,机器学习分类,深度学习工具分类,AI应用分类,智能工具分类":"AI tools categories,artificial intelligence categories,AI tool types,machine learning categories,deep learning tool categories,AI application categories,smart tool categories"}}}async function x(e="en"){try{let t=await (0,o.A)({locale:e,namespace:"categories"}),r=await p.u.getCategories();if(r.success&&r.data)return{categories:r.data.categories.map(e=>{let r=m.RI[e.id]||{description:"优质AI工具集合",icon:"\uD83D\uDD27",color:"#6B7280"};return{_id:e.id,name:t(`category_names.${e.id}`),slug:e.id,description:t(`category_descriptions.${e.id}`),icon:r.icon,color:r.color,toolCount:e.count}}),error:null};return{categories:[],error:r.error||"Failed to fetch categories list"}}catch(e){return console.error("Failed to fetch categories:",e),{categories:[],error:"Failed to fetch categories list, please try again later"}}}async function f({params:e}){let{locale:t}=await e,{categories:r,error:a}=await x(t),i=await (0,o.A)({locale:t,namespace:"categories"}),n=(0,g.hC)([{name:i("breadcrumb_home"),url:"/"},{name:i("breadcrumb_categories"),url:"/categories"}]),l={"@context":"https://schema.org","@type":"ItemList",name:i("structured_data_name"),description:i("structured_data_description"),numberOfItems:r.length,itemListElement:r.map((e,r)=>({"@type":"ListItem",position:r+1,item:{"@type":"Thing",name:e.name,description:e.description,url:`${process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com"}/${t}/categories/${e.slug}`,additionalProperty:{"@type":"PropertyValue",name:i("structured_data_tool_count"),value:e.toolCount}}}))};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(n)}}),(0,s.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(l)}}),(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,s.jsx)("nav",{className:"flex","aria-label":i("breadcrumb_aria_label"),children:(0,s.jsxs)("ol",{className:"inline-flex items-center space-x-1 md:space-x-3",children:[(0,s.jsx)("li",{className:"inline-flex items-center",children:(0,s.jsx)("a",{href:"/",className:"inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600",children:i("breadcrumb_home")})}),(0,s.jsx)("li",{children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("svg",{className:"w-6 h-6 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})}),(0,s.jsx)("span",{className:"ml-1 text-sm font-medium text-gray-500 md:ml-2",children:i("breadcrumb_categories")})]})})]})})}),(0,s.jsx)(u,{categories:r,error:a,locale:t})]})}},24222:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var s=r(60687);r(43210);var o=r(12340),a=r(77618);let i=({category:e})=>{let t=(0,a.c3)("common");return(0,s.jsx)(o.N_,{href:`/categories/${e.slug}`,children:(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 hover:scale-105 cursor-pointer",style:{height:"100%"},children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 rounded-lg flex items-center justify-center text-2xl",style:{backgroundColor:e.color||"#3B82F6"},children:(0,s.jsx)("span",{className:"text-white",children:e.icon||"\uD83D\uDD27"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:e.name}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:t("tools_count",{count:e.toolCount})})]})]}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:e.description})]})})})}},26373:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var s=r(61120);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:o,className:a="",children:i,iconNode:d,...u},m)=>(0,s.createElement)("svg",{ref:m,...c,width:t,height:t,stroke:e,strokeWidth:o?24*Number(r)/Number(t):r,className:n("lucide",a),...!i&&!l(u)&&{"aria-hidden":"true"},...u},[...d.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(i)?i:[i]])),u=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...a},l)=>(0,s.createElement)(d,{ref:l,iconNode:t,className:n(`lucide-${o(i(e))}`,`lucide-${e}`,r),...a}));return r.displayName=i(e),r}},27370:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=r(65239),o=r(48088),a=r(88170),i=r.n(a),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let c={children:["",{children:["[locale]",{children:["categories",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,19995)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/categories/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/categories/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/[locale]/categories/page",pathname:"/[locale]/categories",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38284:(e,t,r)=>{Promise.resolve().then(r.bind(r,39130)),Promise.resolve().then(r.bind(r,45196)),Promise.resolve().then(r.bind(r,24222)),Promise.resolve().then(r.bind(r,11011))},42964:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx","default")},51436:(e,t,r)=>{Promise.resolve().then(r.bind(r,75788)),Promise.resolve().then(r.bind(r,80994)),Promise.resolve().then(r.bind(r,42964)),Promise.resolve().then(r.bind(r,56333))},54290:(e,t,r)=>{"use strict";r.d(t,{B3:()=>o,L2:()=>a,eb:()=>l,hC:()=>n,sU:()=>i});let s=process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com";function o(){return{"@context":"https://schema.org","@type":"WebSite",name:"AI工具导航",description:"发现最好的AI工具，提升您的工作效率和创造力",url:s,potentialAction:{"@type":"SearchAction",target:{"@type":"EntryPoint",urlTemplate:`${s}/tools?search={search_term_string}`},"query-input":"required name=search_term_string"},publisher:{"@type":"Organization",name:"AI工具导航",url:s}}}function a(){return{"@context":"https://schema.org","@type":"Organization",name:"AI工具导航",description:"专业的AI工具发现和推荐平台",url:s,logo:`${s}/logo.png`,sameAs:[]}}function i(e){return{"@context":"https://schema.org","@type":"SoftwareApplication",name:e.name,description:e.description,url:e.website,applicationCategory:"AI工具",operatingSystem:"Web",offers:{"@type":"Offer",price:"free"===e.pricing?"0":void 0,priceCurrency:"USD",availability:"https://schema.org/InStock"},aggregateRating:e.likes?{"@type":"AggregateRating",ratingValue:Math.min(5,Math.max(1,e.likes/10+3)),reviewCount:e.likes,bestRating:5,worstRating:1}:void 0,image:e.logo||`${s}/default-tool-image.jpg`,datePublished:e.launchDate,publisher:{"@type":"Organization",name:"AI工具导航",url:s}}}function n(e){return{"@context":"https://schema.org","@type":"BreadcrumbList",itemListElement:e.map((e,t)=>({"@type":"ListItem",position:t+1,name:e.name,item:`${s}${e.url}`}))}}function l(e,t){return{"@context":"https://schema.org","@type":"ItemList",name:t?`${t} AI工具`:"AI工具列表",description:t?`发现最好的${t} AI工具`:"发现最好的AI工具",numberOfItems:e.length,itemListElement:e.map((e,t)=>({"@type":"ListItem",position:t+1,item:{"@type":"SoftwareApplication",name:e.name,description:e.description,url:`${s}/tools/${e._id}`,image:e.logo||`${s}/default-tool-image.jpg`}}))}}},56333:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx","default")},60366:(e,t,r)=>{"use strict";r.d(t,{BB:()=>i,PZ:()=>n,RI:()=>c,ut:()=>l});var s=r(64348);let o=[{slug:"text-generation",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"data-analysis",icon:"\uD83D\uDCCA",color:"#F59E0B"},{slug:"audio-processing",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"education",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"marketing",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"productivity",icon:"⚡",color:"#14B8A6"},{slug:"customer-service",icon:"\uD83C\uDFA7",color:"#F59E0B"}];async function a(e){let t=await (0,s.A)({locale:e,namespace:"categories"});return o.map(e=>({slug:e.slug,name:t(`category_names.${e.slug}`),description:t(`category_descriptions.${e.slug}`),icon:e.icon,color:e.color}))}async function i(e){return(await a(e)).map(e=>({value:e.slug,label:e.name}))}async function n(e,t){return(await a(t)).find(t=>t.slug===e)}let l=o.map(e=>e.slug),c=o.reduce((e,t)=>(e[t.slug]=t,e),{})},62688:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var s=r(43210);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:o,className:a="",children:i,iconNode:d,...u},m)=>(0,s.createElement)("svg",{ref:m,...c,width:t,height:t,stroke:e,strokeWidth:o?24*Number(r)/Number(t):r,className:n("lucide",a),...!i&&!l(u)&&{"aria-hidden":"true"},...u},[...d.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(i)?i:[i]])),u=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...a},l)=>(0,s.createElement)(d,{ref:l,iconNode:t,className:n(`lucide-${o(i(e))}`,`lucide-${e}`,r),...a}));return r.displayName=i(e),r}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},93613:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,4999,9658,6435,6699,8232,2585],()=>r(27370));module.exports=s})();