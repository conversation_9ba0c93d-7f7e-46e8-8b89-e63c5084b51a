(()=>{var e={};e.id=2797,e.ids=[2797],e.modules={124:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var i=r(65239),s=r(48088),a=r(88170),o=r.n(a),l=r(30893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);r.d(t,n);let c={children:["",{children:["[locale]",{children:["profile",{children:["liked",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,77405)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/liked/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/liked/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new i.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/[locale]/profile/liked/page",pathname:"/[locale]/profile/liked",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},2952:(e,t,r)=>{"use strict";r.d(t,{iu:()=>l,ng:()=>n,Ay:()=>o});var i=r(60687);r(43210);var s=r(31261),a=r.n(s);function o({src:e,alt:t,width:r,height:s,className:o="",priority:l=!1,fill:n=!1,sizes:c,placeholder:d="empty",blurDataURL:u,fallbackSrc:m="/images/placeholder.svg"}){let p={src:e,alt:t,className:o,priority:l,placeholder:"blur"===d?"blur":"empty",blurDataURL:u||("blur"===d?"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojZjNmNGY2O3N0b3Atb3BhY2l0eToxIiAvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNlNWU3ZWI7c3RvcC1vcGFjaXR5OjEiIC8+CiAgICA8L2xpbmVhckdyYWRpZW50PgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0idXJsKCNncmFkaWVudCkiIC8+Cjwvc3ZnPg==":void 0),sizes:c||(n?"100vw":void 0)};return n?(0,i.jsx)("div",{className:"relative overflow-hidden",children:(0,i.jsx)(a(),{...p,fill:!0,style:{objectFit:"cover"}})}):(0,i.jsx)(a(),{...p,width:r,height:s})}let l={avatar:{width:40,height:40},avatarLarge:{width:80,height:80},toolLogo:{width:52,height:52},toolLogoLarge:{width:128,height:128},thumbnail:{width:200,height:150},card:{width:300,height:200},hero:{width:1200,height:600}},n={avatar:"40px",toolLogo:"52px",thumbnail:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",card:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",hero:"100vw",full:"100vw"}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7485:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var i=r(60687);r(43210);var s=r(12340),a=r(77618),o=r(82136),l=r(23877),n=r(2328);function c({toolId:e,initialLikes:t=0,initialLiked:r=!1,onLoginRequired:c,onUnlike:d,isInLikedPage:u=!1,showCount:m=!0,size:p="md"}){let{data:g}=(0,o.useSession)(),{getToolState:x,initializeToolState:h,toggleLike:v}=(0,n.X)(),f=(0,s.a8)(),y=(0,a.c3)("common");f?.startsWith("/en");let b=x(e),w=async()=>{if(!g)return void c?.();if(b.loading)return;let t=b.liked;await v(e,u)&&u&&t&&d&&d(e)},A=(()=>{switch(p){case"sm":return{button:"p-1.5",icon:"h-4 w-4",text:"text-sm"};case"lg":return{button:"p-3",icon:"h-6 w-6",text:"text-lg"};default:return{button:"p-2",icon:"h-5 w-5",text:"text-base"}}})();return(0,i.jsxs)("button",{onClick:w,disabled:b.loading,className:`
        ${A.button}
        inline-flex items-center space-x-1
        ${b.liked?"text-red-500 hover:text-red-600":"text-gray-400 hover:text-red-500"}
        transition-colors duration-200
        disabled:opacity-50 disabled:cursor-not-allowed
      `,title:y(b.liked?"unlike":"like"),children:[b.loading?(0,i.jsx)("div",{className:`${A.icon} animate-spin rounded-full border-2 border-gray-300 border-t-red-500`}):b.liked?(0,i.jsx)(l.Mbv,{className:A.icon}):(0,i.jsx)(l.sOK,{className:A.icon}),m&&(0,i.jsx)("span",{className:`${A.text} font-medium`,children:b.likes})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11011:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var i=r(60687),s=r(93613),a=r(11860);function o({message:e,onClose:t,className:r=""}){return(0,i.jsx)("div",{className:`bg-red-50 border border-red-200 rounded-lg p-4 ${r}`,children:(0,i.jsxs)("div",{className:"flex items-start",children:[(0,i.jsx)(s.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,i.jsx)("div",{className:"flex-1",children:(0,i.jsx)("p",{className:"text-red-800 text-sm",children:e})}),t&&(0,i.jsx)("button",{onClick:t,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,i.jsx)(a.A,{className:"w-4 h-4"})})]})})}},11723:e=>{"use strict";e.exports=require("querystring")},11860:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>n});var i=r(36344),s=r(65752),a=r(13581),o=r(75745),l=r(17063);let n={...!1,providers:[(0,i.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,s.A)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET}),(0,a.A)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Code",type:"text"},token:{label:"Token",type:"text"}},async authorize(e){if(!e?.email||!e?.code||!e?.token)return null;try{await (0,o.A)();let t=await l.A.findOne({email:e.email.toLowerCase(),emailVerificationExpires:{$gt:new Date}});if(!t)return null;let r=t.emailVerificationToken;if(!r||!r.includes(":"))return null;let[i,s]=r.split(":");if(i!==e.token||s!==e.code)return null;return t.emailVerified=!0,t.emailVerificationToken=void 0,t.emailVerificationExpires=void 0,t.lastLoginAt=new Date,t.accounts.some(e=>"email"===e.provider)||t.accounts.push({provider:"email",providerId:"email",providerAccountId:t.email}),await t.save(),{id:t._id.toString(),email:t.email,name:t.name,image:t.avatar,role:t.role}}catch(e){return console.error("Email code authorization error:",e),null}}})],session:{strategy:"jwt"},callbacks:{async signIn({user:e,account:t,profile:r}){if(t?.provider==="email-code")return!0;await (0,o.A)();try{let i=await l.A.findOne({email:e.email});return i?i.lastLoginAt=new Date:i=new l.A({email:e.email,name:e.name||r?.name||"User",avatar:e.image||r?.image,emailVerified:!0,lastLoginAt:new Date}),await i.save(),t&&"email-code"!==t.provider&&(i.addAccount({provider:t.provider,providerId:t.provider,providerAccountId:t.providerAccountId||t.id||"",accessToken:t.access_token,refreshToken:t.refresh_token,expiresAt:t.expires_at?new Date(1e3*t.expires_at):void 0}),await i.save()),!0}catch(e){return console.error("Sign in error:",e),!1}},jwt:async({token:e,user:t})=>(t&&(e.userId=t.id,e.role=t.role||"user"),e),session:async({session:e,token:t})=>(t&&e.user&&(e.user.id=t.userId,e.user.role=t.role),e)},pages:{signIn:"/auth/signin",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET}},13861:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},17063:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var i=r(56037),s=r.n(i);let a=new i.Schema({provider:{type:String,required:!0,enum:["google","github","email"]},providerId:{type:String,required:!0},providerAccountId:{type:String,required:!0},accessToken:String,refreshToken:String,expiresAt:Date},{_id:!1}),o=new i.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,validate:{validator:function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)},message:"Please enter a valid email address"}},name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},avatar:{type:String,trim:!0},role:{type:String,required:!0,enum:["user","admin"],default:"user"},isActive:{type:Boolean,default:!0},emailVerified:{type:Boolean,default:!1},emailVerificationToken:{type:String,trim:!0},emailVerificationExpires:{type:Date},accounts:[a],submittedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],likedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],comments:[{type:i.Schema.Types.ObjectId,ref:"Comment"}],lastLoginAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});o.index({email:1}),o.index({role:1}),o.index({emailVerificationToken:1}),o.index({"accounts.provider":1,"accounts.providerAccountId":1}),o.methods.addAccount=function(e){let t=this.accounts.find(t=>t.provider===e.provider&&t.providerAccountId===e.providerAccountId);t?Object.assign(t,e):this.accounts.push(e)},o.methods.removeAccount=function(e,t){this.accounts=this.accounts.filter(r=>r.provider!==e||r.providerAccountId!==t)};let l=s().models.User||s().model("User",o)},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25334:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},26860:(e,t,r)=>{"use strict";r.d(t,{J2:()=>a,VP:()=>o});var i=r(77618);let s=[{slug:"text-generation",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"data-analysis",icon:"\uD83D\uDCCA",color:"#F59E0B"},{slug:"audio-processing",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"education",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"marketing",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"productivity",icon:"⚡",color:"#14B8A6"},{slug:"customer-service",icon:"\uD83C\uDFA7",color:"#F59E0B"}];function a(){let e=(0,i.c3)("categories"),t=(function(){let e=(0,i.c3)("categories");return s.map(t=>({slug:t.slug,name:e(`category_names.${t.slug}`),description:e(`category_descriptions.${t.slug}`),icon:t.icon,color:t.color}))})().map(e=>({value:e.slug,label:e.name}));return[{value:"",label:e("all_categories")},...t]}function o(e){return(0,i.c3)("categories")(`category_names.${e}`)||e}async function l(e){let t=await getTranslations({locale:e,namespace:"categories"});return s.map(e=>({slug:e.slug,name:t(`category_names.${e.slug}`),description:t(`category_descriptions.${e.slug}`),icon:e.icon,color:e.color}))}s.map(e=>e.slug),s.reduce((e,t)=>(e[t.slug]=t,e),{})},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30762:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var i=r(56037),s=r.n(i),a=r(60366);let o=new i.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,maxlength:[100,"Tool name cannot exceed 100 characters"]},tagline:{type:String,trim:!0,maxlength:[200,"Tagline cannot exceed 200 characters"]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},longDescription:{type:String,trim:!0,maxlength:[2e3,"Long description cannot exceed 2000 characters"]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:a.ut},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[500,"Pricing details cannot exceed 500 characters"]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},launchDate:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});o.index({status:1,isActive:1}),o.index({category:1,status:1}),o.index({tags:1,status:1}),o.index({submittedBy:1}),o.index({launchDate:-1}),o.index({views:-1}),o.index({likes:-1}),o.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let l=s().models.Tool||s().model("Tool",o)},31261:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return n},getImageProps:function(){return l}});let i=r(14985),s=r(44953),a=r(46533),o=i._(r(1933));function l(e){let{props:t}=(0,s.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let n=a.Image},32774:(e,t,r)=>{"use strict";r.d(t,{default:()=>w});var i=r(60687),s=r(43210),a=r(16189),o=r(12340),l=r(11011),n=r(26860),c=r(28559),d=r(99270),u=r(80462),m=r(67760),p=r(25334),g=r(13861),x=r(7485),h=r(2952),v=r(94865),f=r(77618);let y=({tool:e,onLoginRequired:t,onUnlike:r,isInLikedPage:s=!1,locale:a="en"})=>{let l=(0,f.c3)("common");return(0,i.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200",style:{height:"100%"},children:(0,i.jsxs)("div",{className:"p-6",children:[(0,i.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[e.logo?(0,i.jsx)(h.Ay,{src:e.logo,alt:`${e.name} logo`,width:h.iu.toolLogo.width,height:h.iu.toolLogo.height,className:"rounded-lg object-cover",sizes:h.ng.toolLogo,placeholder:"blur"}):(0,i.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,i.jsx)("span",{className:"text-white font-bold text-lg",children:e.name.charAt(0).toUpperCase()})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:e.name}),(0,i.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${(0,v.Ef)(e.pricing)}`,children:(0,v.mV)(e.pricing)})]})]}),(0,i.jsx)("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-blue-600 transition-colors",children:(0,i.jsx)(p.A,{className:"h-5 w-5"})})]}),(0,i.jsx)("p",{className:"text-gray-600 text-sm mb-4 line-clamp-2",children:e.description}),(0,i.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[e.tags.slice(0,3).map((e,t)=>(0,i.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:e},t)),e.tags.length>3&&(0,i.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:["+",e.tags.length-3]})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,i.jsx)(g.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{children:e.views})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,i.jsx)(m.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{children:e.likes})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(x.default,{toolId:e._id,initialLikes:e.likes,initialLiked:s,onLoginRequired:t,onUnlike:r,isInLikedPage:s}),(0,i.jsx)(o.N_,{href:`/tools/${e._id}`,className:"inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors",children:l("view_details")})]})]})]})})};function b({slug:e}){let t=(0,n.VP)(e);return(0,i.jsx)(i.Fragment,{children:t})}function w({initialTools:e}){let t=(0,a.useParams)(),r=t?.locale||"zh",[n,p]=(0,s.useState)(e),[g,x]=(0,s.useState)(e),[h,v]=(0,s.useState)(""),[f,w]=(0,s.useState)(""),[A,j]=(0,s.useState)("all"),N=async e=>{try{p(t=>t.filter(t=>t._id!==e)),v("")}catch(e){console.error("Error unliking tool:",e),v("取消收藏失败，请重试")}},I=Array.from(new Set(n.map(e=>e.category))),k="en"===r?{title:"My Favorites",subtitle:`Your favorite AI tools (${n.length})`,searchPlaceholder:"Search favorite tools...",allCategories:"All Categories",noToolsFound:"No matching tools found",noToolsYet:"No favorite tools yet",adjustFilters:"Try adjusting your search criteria or filters",startExploring:"Start exploring and favorite your preferred AI tools!",browseTools:"Browse Tools"}:{title:"我的收藏",subtitle:`您收藏的AI工具 (${n.length})`,searchPlaceholder:"搜索收藏的工具...",allCategories:"所有分类",noToolsFound:"没有找到匹配的工具",noToolsYet:"还没有收藏任何工具",adjustFilters:"尝试调整搜索条件或筛选器",startExploring:"开始探索并收藏您喜欢的AI工具吧！",browseTools:"浏览工具"};return(0,i.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,i.jsx)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{className:"flex items-center mb-2",children:[(0,i.jsx)(o.N_,{href:"/profile",className:"mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors",children:(0,i.jsx)(c.A,{className:"h-5 w-5"})}),(0,i.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:k.title})]}),(0,i.jsx)("p",{className:"text-lg text-gray-600",children:k.subtitle})]})}),(0,i.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,i.jsx)("div",{className:"flex-1",children:(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,i.jsx)(d.A,{className:"h-5 w-5 text-gray-400"})}),(0,i.jsx)("input",{type:"text",placeholder:k.searchPlaceholder,value:f,onChange:e=>w(e.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})}),(0,i.jsx)("div",{className:"sm:w-48",children:(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,i.jsx)(u.A,{className:"h-5 w-5 text-gray-400"})}),(0,i.jsxs)("select",{value:A,onChange:e=>j(e.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,i.jsx)("option",{value:"all",children:k.allCategories}),I.map(e=>(0,i.jsx)("option",{value:e,children:(0,i.jsx)(b,{slug:e})},e))]})]})})]})}),h&&(0,i.jsx)(l.default,{message:h,onClose:()=>v(""),className:"mb-6"}),g.length>0?(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:g.map(e=>(0,i.jsx)(y,{tool:e,onUnlike:N,isInLikedPage:!0},e._id))}):(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center",children:[(0,i.jsx)("div",{className:"text-gray-400 mb-4",children:(0,i.jsx)(m.A,{className:"h-12 w-12 mx-auto"})}),(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:f||"all"!==A?k.noToolsFound:k.noToolsYet}),(0,i.jsx)("p",{className:"text-gray-600 mb-4",children:f||"all"!==A?k.adjustFilters:k.startExploring}),!f&&"all"===A&&(0,i.jsx)(o.N_,{href:"/tools",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:k.browseTools})]})]})}},33873:e=>{"use strict";e.exports=require("path")},41801:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/profile/LikedToolsClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/profile/LikedToolsClient.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},55779:(e,t,r)=>{Promise.resolve().then(r.bind(r,75788)),Promise.resolve().then(r.bind(r,80994)),Promise.resolve().then(r.bind(r,41801))},56037:e=>{"use strict";e.exports=require("mongoose")},60366:(e,t,r)=>{"use strict";r.d(t,{BB:()=>o,PZ:()=>l,RI:()=>c,ut:()=>n});var i=r(64348);let s=[{slug:"text-generation",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"data-analysis",icon:"\uD83D\uDCCA",color:"#F59E0B"},{slug:"audio-processing",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"education",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"marketing",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"productivity",icon:"⚡",color:"#14B8A6"},{slug:"customer-service",icon:"\uD83C\uDFA7",color:"#F59E0B"}];async function a(e){let t=await (0,i.A)({locale:e,namespace:"categories"});return s.map(e=>({slug:e.slug,name:t(`category_names.${e.slug}`),description:t(`category_descriptions.${e.slug}`),icon:e.icon,color:e.color}))}async function o(e){return(await a(e)).map(e=>({value:e.slug,label:e.name}))}async function l(e,t){return(await a(t)).find(t=>t.slug===e)}let n=s.map(e=>e.slug),c=s.reduce((e,t)=>(e[t.slug]=t,e),{})},62688:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var i=r(43210);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),o=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),n=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,i.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:s,className:a="",children:o,iconNode:d,...u},m)=>(0,i.createElement)("svg",{ref:m,...c,width:t,height:t,stroke:e,strokeWidth:s?24*Number(r)/Number(t):r,className:l("lucide",a),...!o&&!n(u)&&{"aria-hidden":"true"},...u},[...d.map(([e,t])=>(0,i.createElement)(e,t)),...Array.isArray(o)?o:[o]])),u=(e,t)=>{let r=(0,i.forwardRef)(({className:r,...a},n)=>(0,i.createElement)(d,{ref:n,iconNode:t,className:l(`lucide-${s(o(e))}`,`lucide-${e}`,r),...a}));return r.displayName=o(e),r}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67760:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},69851:(e,t,r)=>{Promise.resolve().then(r.bind(r,39130)),Promise.resolve().then(r.bind(r,45196)),Promise.resolve().then(r.bind(r,32774))},74075:e=>{"use strict";e.exports=require("zlib")},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var i=r(56037),s=r.n(i);let a=process.env.MONGODB_URI;if(!a)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let o=global.mongoose;o||(o=global.mongoose={conn:null,promise:null});let l=async function(){if(o.conn)return o.conn;o.promise||(o.promise=s().connect(a,{bufferCommands:!1}).then(e=>e));try{o.conn=await o.promise}catch(e){throw o.promise=null,e}return o.conn}},77405:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var i=r(37413),s=r(61120),a=r(35426),o=r(78878),l=r(12909),n=r(75745),c=r(17063),d=r(30762),u=r(41801);async function m(e){await (0,n.A)();let t=await c.A.findOne({email:e});if(!t)return[];let r=t.likedTools||[];return 0===r.length?[]:(await d.A.find({_id:{$in:r},isActive:!0,status:"approved"}).sort({updatedAt:-1}).lean()).map(e=>({_id:e._id.toString(),name:e.name,description:e.description,website:e.website,logo:e.logo,category:e.category,tags:e.tags||[],pricing:e.pricing,views:e.views||0,likes:e.likes||0,status:e.status,isActive:e.isActive,createdAt:e.createdAt,updatedAt:e.updatedAt,launchDate:e.launchDate,submittedBy:e.submittedBy?.toString(),submittedAt:e.submittedAt||e.createdAt,reviewedBy:e.reviewedBy?.toString(),reviewNotes:e.reviewNotes,rejectReason:e.rejectReason,likedBy:e.likedBy?.map(e=>e.toString())||[],tagline:e.tagline}))}async function p({params:e}){let t=await (0,a.getServerSession)(l.N),{locale:r}=await e;t?.user?.email||(0,o.V2)({href:"/",locale:r});let n=await m(t?.user?.email||"");return(0,i.jsx)(s.Fragment,{children:(0,i.jsx)(u.default,{initialTools:n})})}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80462:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},81630:e=>{"use strict";e.exports=require("http")},93613:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")},94865:(e,t,r)=>{"use strict";r.d(t,{$g:()=>d,Ef:()=>n,Y$:()=>l,kX:()=>i,mV:()=>c,tF:()=>u,v4:()=>o,vS:()=>s});let i={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},s=[{id:"free",title:"免费发布",description:i.FREE_LAUNCH.description,price:i.FREE_LAUNCH.displayPrice,features:i.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:i.PRIORITY_LAUNCH.description,price:i.PRIORITY_LAUNCH.displayPrice,features:i.PRIORITY_LAUNCH.features,recommended:!0}],a={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},o=[{value:"",label:"所有价格"},{value:a.FREE.value,label:a.FREE.label},{value:a.FREEMIUM.value,label:a.FREEMIUM.label},{value:a.PAID.value,label:a.PAID.label}],l=[{value:a.FREE.value,label:a.FREE.label},{value:a.FREEMIUM.value,label:a.FREEMIUM.label},{value:a.PAID.value,label:a.PAID.label}],n=e=>{switch(e){case a.FREE.value:return a.FREE.color;case a.FREEMIUM.value:return a.FREEMIUM.color;case a.PAID.value:return a.PAID.color;default:return"bg-gray-100 text-gray-800"}},c=e=>{switch(e){case a.FREE.value:return a.FREE.label;case a.FREEMIUM.value:return a.FREEMIUM.label;case a.PAID.value:return a.PAID.label;default:return e}},d=(e,t)=>0===e?"zh"===t?"免费":"Free":`\xa5${e}`,u=(e,t="cny")=>new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)},99270:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4243,4999,9658,6435,6699,8232,3136,6533,2585],()=>r(124));module.exports=i})();