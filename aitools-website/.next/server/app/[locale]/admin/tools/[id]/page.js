(()=>{var e={};e.id=9713,e.ids=[9713],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3650:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=s(65239),a=s(48088),l=s(88170),i=s.n(l),o=s(30893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);s.d(t,n);let c={children:["",{children:["[locale]",{children:["admin",{children:["tools",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,28619)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/admin/tools/[id]/page",pathname:"/[locale]/admin/tools/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10806:(e,t,s)=>{"use strict";function r(){if(process.env.NEXT_PUBLIC_APP_URL)return process.env.NEXT_PUBLIC_APP_URL;{if(process.env.VERCEL_URL)return`https://${process.env.VERCEL_URL}`;if(process.env.NETLIFY&&process.env.URL)return process.env.URL;if(process.env.RAILWAY_STATIC_URL)return process.env.RAILWAY_STATIC_URL;if(process.env.APP_URL)return process.env.APP_URL;let e=process.env.PORT||"3001";return`http://localhost:${e}`}}function a(){if(process.env.NEXT_PUBLIC_API_BASE_URL)return process.env.NEXT_PUBLIC_API_BASE_URL;let e=r();return`${e}/api`}function l(){return"production"}function i(){return"development"===l()}s.d(t,{u:()=>d});let o={baseUrl:r(),apiBaseUrl:a(),nextAuthUrl:process.env.NEXTAUTH_URL?process.env.NEXTAUTH_URL:r(),environment:l(),isDevelopment:i(),isProduction:"production"===l(),port:process.env.PORT||"3001"};i()&&(console.log("\uD83D\uDD27 Dynamic Environment Configuration:"),console.log("  Base URL:",o.baseUrl),console.log("  API Base URL:",o.apiBaseUrl),console.log("  NextAuth URL:",o.nextAuthUrl),console.log("  Environment:",o.environment),console.log("  Port:",o.port));let n=a();class c{constructor(e=n){this.baseURL=e}async request(e,t={}){try{let s=`${this.baseURL}${e}`,r={headers:{"Content-Type":"application/json",...t.headers},...t},a=await fetch(s,r),l=await a.json();if(!a.ok)throw Error(l.error||`HTTP error! status: ${a.status}`);return l}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=t.toString();return this.request(`/tools${s?`?${s}`:""}`)}async getTool(e){return this.request(`/tools/${e}`)}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,t){return this.request(`/tools/${e}`,{method:"PUT",body:JSON.stringify(t)})}async deleteTool(e){return this.request(`/tools/${e}`,{method:"DELETE"})}async getLikedTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=t.toString();return this.request(`/user/liked-tools${s?`?${s}`:""}`)}async getAdminTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=t.toString();return this.request(`/admin/tools${s?`?${s}`:""}`)}async approveTool(e,t){return this.request(`/admin/tools/${e}/approve`,{method:"POST",body:JSON.stringify(t)})}async rejectTool(e,t){return this.request(`/admin/tools/${e}/reject`,{method:"POST",body:JSON.stringify(t)})}async getAdminStats(e){let t=e?`?timeRange=${e}`:"";return this.request(`/admin/stats${t}`)}async getCategories(){return this.request("/categories")}async getOrder(e){return this.request(`/orders/${e}`)}async processOrderPayment(e,t){return this.request(`/orders/${e}/pay`,{method:"POST",body:JSON.stringify(t)})}}let d=new c},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11011:(e,t,s)=>{"use strict";s.d(t,{default:()=>i});var r=s(60687),a=s(93613),l=s(11860);function i({message:e,onClose:t,className:s=""}){return(0,r.jsx)("div",{className:`bg-red-50 border border-red-200 rounded-lg p-4 ${s}`,children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-red-800 text-sm",children:e})}),t&&(0,r.jsx)("button",{onClick:t,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,r.jsx)(l.A,{className:"w-4 h-4"})})]})})}},11860:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23845:(e,t,s)=>{Promise.resolve().then(s.bind(s,73265)),Promise.resolve().then(s.bind(s,27619))},26373:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(61120);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),i=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim(),n=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:l="",children:i,iconNode:d,...m},u)=>(0,r.createElement)("svg",{ref:u,...c,width:t,height:t,stroke:e,strokeWidth:a?24*Number(s)/Number(t):s,className:o("lucide",l),...!i&&!n(m)&&{"aria-hidden":"true"},...m},[...d.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(i)?i:[i]])),m=(e,t)=>{let s=(0,r.forwardRef)(({className:s,...l},n)=>(0,r.createElement)(d,{ref:n,iconNode:t,className:o(`lucide-${a(i(e))}`,`lucide-${e}`,s),...l}));return s.displayName=i(e),s}},26919:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(26373).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},27619:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/BackButton.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/BackButton.tsx","default")},28559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},28619:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var r=s(37413);s(61120);var a=s(39916),l=s(64348),i=s(10806),o=s(73265),n=s(27619),c=s(26919),d=s(91142),m=s(53148),u=s(89633),x=s(26373);let h=(0,x.A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]),p=(0,x.A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var g=s(33488),y=s(94014);let f=(0,x.A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);var b=s(40918);async function v(e){try{let t=await i.u.getTool(e);if(t.success&&t.data)return{tool:t.data,error:null};return{tool:null,error:t.error||"Failed to fetch tool"}}catch(e){return console.error("Failed to fetch tool:",e),{tool:null,error:"Failed to fetch tool, please try again later"}}}async function j({params:e}){let{locale:t,id:s}=await e;s&&24===s.length||(0,a.notFound)();let i=await (0,l.A)("admin"),x=await (0,l.A)("categories"),{tool:j,error:w}=await v(s);return w||!j?(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(c.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,r.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:w||i("errors.tool_not_found")})]})}):(0,r.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)(o.default,{tool:j,locale:t}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)(n.default,{}),(0,r.jsx)("div",{className:"flex items-start justify-between",children:(0,r.jsxs)("div",{className:"flex items-start space-x-6",children:[(0,r.jsx)("img",{src:j.logo,alt:j.name,className:"w-24 h-24 rounded-xl object-cover border border-gray-200 shadow-sm"}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:j.name}),(e=>{if("approved"===e.status&&e.launchDate&&new Date(e.launchDate)<=new Date)return(0,r.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800",children:[(0,r.jsx)(d.A,{className:"w-4 h-4 mr-2"}),i("status_labels.published")]});switch(e.status){case"pending":return(0,r.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800",children:[(0,r.jsx)(m.A,{className:"w-4 h-4 mr-2"}),i("status_labels.pending")]});case"approved":return(0,r.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800",children:[(0,r.jsx)(d.A,{className:"w-4 h-4 mr-2"}),i("status_labels.approved")]});case"rejected":return(0,r.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800",children:[(0,r.jsx)(u.A,{className:"w-4 h-4 mr-2"}),i("status_labels.rejected")]});default:return null}})(j)]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600 mb-4",children:[(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:x(`category_names.${j.category}`)||j.category}),(0,r.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:[(0,r.jsx)(h,{className:"w-3 h-3 mr-1"}),i(`pricing_labels.${j.pricing}`)||j.pricing]}),(0,r.jsxs)("a",{href:j.website,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors",children:[(0,r.jsx)(p,{className:"w-4 h-4 mr-1"}),i("actions.visit_website"),(0,r.jsx)(g.A,{className:"w-3 h-3 ml-1"})]})]}),(0,r.jsx)("p",{className:"text-gray-600 max-w-3xl",children:j.tagline})]})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8 mt-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:i("sections.tool_description")}),(0,r.jsxs)("div",{className:"prose max-w-none",children:[(0,r.jsx)("p",{className:"text-gray-700 leading-relaxed",children:j.description}),j.longDescription&&(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:i("sections.detailed_description")}),(0,r.jsx)("p",{className:"text-gray-700 leading-relaxed",children:j.longDescription})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:i("sections.tags")}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:j.tags.map((e,t)=>(0,r.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800",children:[(0,r.jsx)(y.A,{className:"w-3 h-3 mr-1"}),e]},t))})]}),j.screenshots&&j.screenshots.length>0&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:i("sections.screenshot_preview")}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:j.screenshots.map((e,t)=>(0,r.jsx)("img",{src:e,alt:`${j.name} ${i("sections.screenshot_preview")} ${t+1}`,className:"w-full h-48 object-cover rounded-lg border border-gray-200"},t))})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:i("sections.submission_info")}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(f,{className:"w-5 h-5 text-gray-400 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:j.submittedBy}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:i("fields.submitter_id")})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(b.A,{className:"w-5 h-5 text-gray-400 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:new Date(j.submittedAt).toLocaleDateString("zh"===t?"zh-CN":"en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:i("fields.submission_time")})]})]}),j.selectedLaunchDate&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(m.A,{className:"w-5 h-5 text-gray-400 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:new Date(j.selectedLaunchDate).toLocaleDateString("zh"===t?"zh-CN":"en-US")}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:i("fields.selected_launch_date")})]})]}),j.launchDate&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"w-5 h-5 text-gray-400 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:new Date(j.launchDate).toLocaleDateString("zh"===t?"zh-CN":"en-US")}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:i("fields.actual_launch_date")})]})]})]})]}),(0,r.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(c.A,{className:"w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:i("sections.review_guidelines")}),(0,r.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,r.jsxs)("li",{children:["• ",i("guidelines.verify_website")]}),(0,r.jsxs)("li",{children:["• ",i("guidelines.check_description")]}),(0,r.jsxs)("li",{children:["• ",i("guidelines.confirm_category")]}),(0,r.jsxs)("li",{children:["• ",i("guidelines.evaluate_quality")]}),(0,r.jsxs)("li",{children:["• ",i("guidelines.check_duplicates")]})]})]})]})})]})]})]})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31313:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var r=s(60687),a=s(12340),l=s(77618),i=s(28559);function o(){let e=(0,a.rd)(),t=(0,l.c3)("admin");return(0,r.jsxs)("button",{onClick:()=>e.back(),className:"flex items-center text-gray-600 hover:text-gray-900 mb-4 transition-colors",children:[(0,r.jsx)(i.A,{className:"w-4 h-4 mr-2"}),t("actions.back_to_review")]})}},33488:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(26373).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},33873:e=>{"use strict";e.exports=require("path")},35071:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},36997:(e,t,s)=>{Promise.resolve().then(s.bind(s,43799)),Promise.resolve().then(s.bind(s,31313))},40918:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(26373).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},43799:(e,t,s)=>{"use strict";s.d(t,{default:()=>m});var r=s(60687),a=s(43210),l=s(12340),i=s(77618),o=s(11011),n=s(78890),c=s(5336),d=s(35071);function m({tool:e,locale:t}){let s=(0,l.rd)(),m=(0,i.c3)("admin"),[u,x]=(0,a.useState)(""),[h,p]=(0,a.useState)(""),[g,y]=(0,a.useState)(!1),[f,b]=(0,a.useState)(""),[v,j]=(0,a.useState)(!1),w=async()=>{j(!0);try{x("");let t=await fetch(`/api/admin/tools/${e._id}/approve`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({reviewedBy:"admin",reviewNotes:m("success.tool_approved"),launchDate:new Date().toISOString()})}),r=await t.json();r.success?(p(m("success.tool_approved")),s.refresh()):x(r.error||m("errors.approve_failed"))}catch(e){x(m("errors.network_error"))}finally{j(!1)}},N=async()=>{if(!f.trim())return void x(m("errors.reject_reason_required"));j(!0);try{x("");let t=await fetch(`/api/admin/tools/${e._id}/reject`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({reviewedBy:"admin",rejectReason:f})}),r=await t.json();r.success?(p(m("success.tool_rejected")),y(!1),b(""),s.refresh()):x(r.error||m("errors.reject_failed"))}catch(e){x(m("errors.network_error"))}finally{j(!1)}};return(0,r.jsxs)(r.Fragment,{children:[h&&(0,r.jsx)(n.A,{message:h,onClose:()=>p(""),className:"mb-6"}),u&&(0,r.jsx)(o.default,{message:u,onClose:()=>x(""),className:"mb-6"}),"pending"===e.status&&(0,r.jsxs)("div",{className:"flex space-x-3 mb-6",children:[(0,r.jsxs)("button",{onClick:w,disabled:v,className:"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center",children:[(0,r.jsx)(c.A,{className:"w-4 h-4 mr-2"}),v?m("actions.processing"):m("actions.approve")]}),(0,r.jsxs)("button",{onClick:()=>y(!0),disabled:v,className:"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center",children:[(0,r.jsx)(d.A,{className:"w-4 h-4 mr-2"}),m("actions.reject")]})]}),g&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:m("reject_modal.title")}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:m("reject_modal.description")}),(0,r.jsx)("textarea",{value:f,onChange:e=>b(e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:m("reject_modal.placeholder")}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,r.jsx)("button",{onClick:()=>{y(!1),b("")},disabled:v,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors",children:m("actions.cancel")}),(0,r.jsx)("button",{onClick:N,disabled:!f.trim()||v,className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:v?m("actions.processing"):m("actions.confirm_reject")})]})]})})]})}},53148:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(26373).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},62688:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),i=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim(),n=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:l="",children:i,iconNode:d,...m},u)=>(0,r.createElement)("svg",{ref:u,...c,width:t,height:t,stroke:e,strokeWidth:a?24*Number(s)/Number(t):s,className:o("lucide",l),...!i&&!n(m)&&{"aria-hidden":"true"},...m},[...d.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(i)?i:[i]])),m=(e,t)=>{let s=(0,r.forwardRef)(({className:s,...l},n)=>(0,r.createElement)(d,{ref:n,iconNode:t,className:o(`lucide-${a(i(e))}`,`lucide-${e}`,s),...l}));return s.displayName=i(e),s}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73265:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/AdminToolDetailClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/AdminToolDetailClient.tsx","default")},78890:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(60687),a=s(5336),l=s(11860);function i({message:e,onClose:t,className:s=""}){return(0,r.jsx)("div",{className:`bg-green-50 border border-green-200 rounded-lg p-4 ${s}`,children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.A,{className:"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-green-800 text-sm",children:e})}),t&&(0,r.jsx)("button",{onClick:t,className:"ml-3 text-green-400 hover:text-green-600 transition-colors",children:(0,r.jsx)(l.A,{className:"w-4 h-4"})})]})})}},79551:e=>{"use strict";e.exports=require("url")},89633:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(26373).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},91142:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(26373).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},93613:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94014:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(26373).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,4999,9658,6435,6699,8232,2585],()=>s(3650));module.exports=r})();