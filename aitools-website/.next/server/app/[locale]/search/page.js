(()=>{var e={};e.id=5230,e.ids=[5230],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8893:(e,t,r)=>{Promise.resolve().then(r.bind(r,48021))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27390:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=r(65239),a=r(48088),o=r(88170),n=r.n(o),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c={children:["",{children:["[locale]",{children:["search",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,46252)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/search/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/search/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/search/page",pathname:"/[locale]/search",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},46252:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,generateMetadata:()=>l});var s=r(37413);r(61120);var a=r(39916),o=r(64348),n=r(49039),i=r(10806);async function l({params:e,searchParams:t}){let{locale:r}=await e,s=(await t).q||"",a=await (0,o.A)({locale:r,namespace:"search"});if(!s.trim())return{title:a("page_title"),description:a("page_description")};let n=a("page_title_with_query",{query:s}),i=a("page_description_with_query",{query:s});return{title:n,description:i,openGraph:{title:n,description:i}}}async function c({params:e,searchParams:t}){let{locale:r}=await e,o=await t,l=o.q||"",c=parseInt(o.page||"1"),d=o.category||"",u=o.sort||"createdAt";if(!l.trim())return(0,s.jsx)(n.default,{initialQuery:"",initialResults:null,initialCategories:[],locale:r});try{let[e,t]=await Promise.all([i.u.getTools({search:l,page:c,limit:12,category:d||void 0,sort:u,order:"desc"}),i.u.getCategories()]);if(!e.success)throw Error(e.error||"获取搜索结果失败");if(!t.success)throw Error(t.error||"获取分类失败");return(0,s.jsx)(n.default,{initialQuery:l,initialResults:e.data,initialCategories:t.data?.categories||[],initialPage:c,initialCategory:d,initialSort:u,locale:r})}catch(e){console.error("Search page error:",e),(0,a.notFound)()}}},48021:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var s=r(60687),a=r(43210),o=r(12340),n=r(16189),i=r(77618),l=r(74820),c=r(56976),d=r(99270);function u({initialQuery:e,initialResults:t,initialCategories:r,initialPage:u=1,initialCategory:p="",initialSort:h="createdAt",locale:m}){let g=(0,o.rd)(),x=(0,n.useSearchParams)(),y=(0,i.c3)("search"),[b,f]=(0,a.useState)(e),[v,w]=(0,a.useState)(t),[_]=(0,a.useState)(r),[P,j]=(0,a.useState)(p),[N,S]=(0,a.useState)(h),[U,A]=(0,a.useState)("grid"),[T,L]=(0,a.useState)(!1),[R,E]=(0,a.useState)(u),q=e=>{let t=new URLSearchParams(x||"");Object.entries(e).forEach(([e,r])=>{r?t.set(e,r):t.delete(e)}),g.push(`/search?${t.toString()}`)},C=async(e="",t=1,r="",s="createdAt")=>{L(!0);try{let a=await c.u.getTools({search:e,page:t,limit:12,category:r||void 0,sort:s,order:"desc"});a.success?w(a.data||null):(console.error("Search failed:",a.error),w(null))}catch(e){console.error("Search error:",e),w(null)}finally{L(!1)}},$=e=>{E(e),q({q:b,page:e.toString(),category:P,sort:N}),C(b,e,P,N)};return(0,s.jsx)(a.Fragment,{children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:y("title")}),b&&(0,s.jsxs)("p",{className:"text-lg text-gray-600",children:[y("search_results",{term:b}),v&&` - ${y("found_tools",{count:v.pagination.totalItems})}`]})]}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:(0,s.jsx)("form",{onSubmit:e=>{e?.preventDefault(),E(1),q({q:b?.trim(),page:"1",category:P,sort:N}),C(b?.trim(),1,P,N)},children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"text",placeholder:y("placeholder"),value:b,onChange:e=>f(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,s.jsx)(d.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),(0,s.jsxs)("button",{type:"submit",className:"absolute right-2 top-2 px-4 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",style:{whiteSpace:"nowrap",marginLeft:10},children:[!b.trim()&&y("all"),b.trim()&&y("search_button")]})]})})}),!b&&(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"text-gray-400 mb-4",children:(0,s.jsx)(d.A,{className:"h-12 w-12 mx-auto"})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:y("start_search_title")}),(0,s.jsx)("p",{className:"text-gray-600",children:y("start_search_desc")})]}),T&&(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:y("searching")})]}),!T&&v&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsxs)("p",{className:"text-gray-600",children:[y("results_count",{showing:v.tools.length,total:v.pagination.totalItems}),P&&` ${y("in_category",{category:_.find(e=>e.id===P)?.name||""})}`]})}),v.tools.length>0?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"grid"===U?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8":"space-y-4 mb-8",children:v.tools.map(e=>(0,s.jsx)(l.A,{tool:e},e._id))}),v.pagination.totalPages>1&&(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsxs)("nav",{className:"flex items-center space-x-2",children:[(0,s.jsx)("button",{onClick:()=>$(R-1),disabled:!v.pagination.hasPrevPage,className:"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:y("prev_page")}),(0,s.jsx)("span",{className:"px-3 py-2 text-sm text-gray-700",children:y("page_info",{current:R,total:v.pagination.totalPages})}),(0,s.jsx)("button",{onClick:()=>$(R+1),disabled:!v.pagination.hasNextPage,className:"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:y("next_page")})]})})]}):(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"text-gray-400 mb-4",children:(0,s.jsx)(d.A,{className:"h-12 w-12 mx-auto"})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:y("no_results")}),(0,s.jsx)("p",{className:"text-gray-600",children:y("try_different_keywords")})]})]})]})})}},49039:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/search/SearchPageClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/search/SearchPageClient.tsx","default")},56976:(e,t,r)=>{"use strict";function s(){if(process.env.NEXT_PUBLIC_APP_URL)return process.env.NEXT_PUBLIC_APP_URL;{if(process.env.VERCEL_URL)return`https://${process.env.VERCEL_URL}`;if(process.env.NETLIFY&&process.env.URL)return process.env.URL;if(process.env.RAILWAY_STATIC_URL)return process.env.RAILWAY_STATIC_URL;if(process.env.APP_URL)return process.env.APP_URL;let e=process.env.PORT||"3001";return`http://localhost:${e}`}}function a(){if(process.env.NEXT_PUBLIC_API_BASE_URL)return process.env.NEXT_PUBLIC_API_BASE_URL;let e=s();return`${e}/api`}function o(){return"production"}function n(){return"development"===o()}r.d(t,{u:()=>d});let i={baseUrl:s(),apiBaseUrl:a(),nextAuthUrl:process.env.NEXTAUTH_URL?process.env.NEXTAUTH_URL:s(),environment:o(),isDevelopment:n(),isProduction:"production"===o(),port:process.env.PORT||"3001"};n()&&(console.log("\uD83D\uDD27 Dynamic Environment Configuration:"),console.log("  Base URL:",i.baseUrl),console.log("  API Base URL:",i.apiBaseUrl),console.log("  NextAuth URL:",i.nextAuthUrl),console.log("  Environment:",i.environment),console.log("  Port:",i.port));let l=a();class c{constructor(e=l){this.baseURL=e}async request(e,t={}){try{let r=`${this.baseURL}${e}`,s={headers:{"Content-Type":"application/json",...t.headers},...t},a=await fetch(r,s),o=await a.json();if(!a.ok)throw Error(o.error||`HTTP error! status: ${a.status}`);return o}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,r])=>{void 0!==r&&t.append(e,r.toString())});let r=t.toString();return this.request(`/tools${r?`?${r}`:""}`)}async getTool(e){return this.request(`/tools/${e}`)}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,t){return this.request(`/tools/${e}`,{method:"PUT",body:JSON.stringify(t)})}async deleteTool(e){return this.request(`/tools/${e}`,{method:"DELETE"})}async getLikedTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,r])=>{void 0!==r&&t.append(e,r.toString())});let r=t.toString();return this.request(`/user/liked-tools${r?`?${r}`:""}`)}async getAdminTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,r])=>{void 0!==r&&t.append(e,r.toString())});let r=t.toString();return this.request(`/admin/tools${r?`?${r}`:""}`)}async approveTool(e,t){return this.request(`/admin/tools/${e}/approve`,{method:"POST",body:JSON.stringify(t)})}async rejectTool(e,t){return this.request(`/admin/tools/${e}/reject`,{method:"POST",body:JSON.stringify(t)})}async getAdminStats(e){let t=e?`?timeRange=${e}`:"";return this.request(`/admin/stats${t}`)}async getCategories(){return this.request("/categories")}async getOrder(e){return this.request(`/orders/${e}`)}async processOrderPayment(e,t){return this.request(`/orders/${e}/pay`,{method:"POST",body:JSON.stringify(t)})}}let d=new c},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},81277:(e,t,r)=>{Promise.resolve().then(r.bind(r,49039))},99270:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,4999,9658,6435,6699,8232,6533,2585,8773],()=>r(27390));module.exports=s})();