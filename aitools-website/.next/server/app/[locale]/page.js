(()=>{var e={};e.id=465,e.ids=[465],e.modules={1322:(e,t)=>{"use strict";function s(e){let{widthInt:t,heightInt:s,blurWidth:r,blurHeight:i,blurDataURL:o,objectFit:a}=e,l=r?40*r:t,n=i?40*i:s,c=l&&n?"viewBox='0 0 "+l+" "+n+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+c+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(c?"none":"contain"===a?"xMidYMid":"cover"===a?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+o+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return s}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9131:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return n}}),s(21122);let r=s(1322),i=s(27894),o=["-moz-initial","fill","none","scale-down",void 0];function a(e){return void 0!==e.default}function l(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function n(e,t){var s,n;let c,d,m,{src:u,sizes:g,unoptimized:h=!1,priority:p=!1,loading:x,className:b,quality:f,width:v,height:j,fill:y=!1,style:w,overrideSrc:_,onLoad:N,onLoadingComplete:I,placeholder:C="empty",blurDataURL:A,fetchPriority:P,decoding:E="async",layout:k,objectFit:R,objectPosition:S,lazyBoundary:z,lazyRoot:M,...O}=e,{imgConf:T,showAltText:B,blurComplete:D,defaultLoader:L}=t,G=T||i.imageConfigDefault;if("allSizes"in G)c=G;else{let e=[...G.deviceSizes,...G.imageSizes].sort((e,t)=>e-t),t=G.deviceSizes.sort((e,t)=>e-t),r=null==(s=G.qualities)?void 0:s.sort((e,t)=>e-t);c={...G,allSizes:e,deviceSizes:t,qualities:r}}if(void 0===L)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let F=O.loader||L;delete O.loader,delete O.srcSet;let Z="__next_img_default"in F;if(Z){if("custom"===c.loader)throw Object.defineProperty(Error('Image with src "'+u+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=F;F=t=>{let{config:s,...r}=t;return e(r)}}if(k){"fill"===k&&(y=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[k];e&&(w={...w,...e});let t={responsive:"100vw",fill:"100vw"}[k];t&&!g&&(g=t)}let U="",W=l(v),q=l(j);if((n=u)&&"object"==typeof n&&(a(n)||void 0!==n.src)){let e=a(u)?u.default:u;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(d=e.blurWidth,m=e.blurHeight,A=A||e.blurDataURL,U=e.src,!y)if(W||q){if(W&&!q){let t=W/e.width;q=Math.round(e.height*t)}else if(!W&&q){let t=q/e.height;W=Math.round(e.width*t)}}else W=e.width,q=e.height}let H=!p&&("lazy"===x||void 0===x);(!(u="string"==typeof u?u:U)||u.startsWith("data:")||u.startsWith("blob:"))&&(h=!0,H=!1),c.unoptimized&&(h=!0),Z&&!c.dangerouslyAllowSVG&&u.split("?",1)[0].endsWith(".svg")&&(h=!0);let J=l(f),V=Object.assign(y?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:R,objectPosition:S}:{},B?{}:{color:"transparent"},w),X=D||"empty"===C?null:"blur"===C?'url("data:image/svg+xml;charset=utf-8,'+(0,r.getImageBlurSvg)({widthInt:W,heightInt:q,blurWidth:d,blurHeight:m,blurDataURL:A||"",objectFit:V.objectFit})+'")':'url("'+C+'")',$=o.includes(V.objectFit)?"fill"===V.objectFit?"100% 100%":"cover":V.objectFit,Y=X?{backgroundSize:$,backgroundPosition:V.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:X}:{},K=function(e){let{config:t,src:s,unoptimized:r,width:i,quality:o,sizes:a,loader:l}=e;if(r)return{src:s,srcSet:void 0,sizes:void 0};let{widths:n,kind:c}=function(e,t,s){let{deviceSizes:r,allSizes:i}=e;if(s){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let r;r=e.exec(s);)t.push(parseInt(r[2]));if(t.length){let e=.01*Math.min(...t);return{widths:i.filter(t=>t>=r[0]*e),kind:"w"}}return{widths:i,kind:"w"}}return"number"!=typeof t?{widths:r,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>i.find(t=>t>=e)||i[i.length-1]))],kind:"x"}}(t,i,a),d=n.length-1;return{sizes:a||"w"!==c?a:"100vw",srcSet:n.map((e,r)=>l({config:t,src:s,quality:o,width:e})+" "+("w"===c?e:r+1)+c).join(", "),src:l({config:t,src:s,quality:o,width:n[d]})}}({config:c,src:u,unoptimized:h,width:W,quality:J,sizes:g,loader:F});return{props:{...O,loading:H?"lazy":x,fetchPriority:P,width:W,height:q,decoding:E,className:b,style:{...V,...Y},sizes:K.sizes,srcSet:K.srcSet,src:_||K.src},meta:{unoptimized:h,priority:p,placeholder:C,fill:y}}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21122:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return s}});let s=e=>{}},22524:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=s(65239),i=s(48088),o=s(88170),a=s.n(o),l=s(30893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);s.d(t,n);let c={children:["",{children:["[locale]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,71599)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/[locale]/page",pathname:"/[locale]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},24222:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var r=s(60687);s(43210);var i=s(12340),o=s(77618);let a=({category:e})=>{let t=(0,o.c3)("common");return(0,r.jsx)(i.N_,{href:`/categories/${e.slug}`,children:(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 hover:scale-105 cursor-pointer",style:{height:"100%"},children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 rounded-lg flex items-center justify-center text-2xl",style:{backgroundColor:e.color||"#3B82F6"},children:(0,r.jsx)("span",{className:"text-white",children:e.icon||"\uD83D\uDD27"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:t("tools_count",{count:e.toolCount})})]})]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:e.description})]})})})}},27894:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{VALID_LOADERS:function(){return s},imageConfigDefault:function(){return r}});let s=["default","imgix","cloudinary","akamai","custom"],r={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32091:(e,t)=>{"use strict";function s(e){var t;let{config:s,src:r,width:i,quality:o}=e,a=o||(null==(t=s.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return s.path+"?url="+encodeURIComponent(r)+"&w="+i+"&q="+a+(r.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}}),s.__next_img_default=!0;let r=s},33873:e=>{"use strict";e.exports=require("path")},42964:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx","default")},45805:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(26373).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},49603:(e,t,s)=>{let{createProxy:r}=s(39844);e.exports=r("/Users/<USER>/workspace/aitools/aitools-website/node_modules/next/dist/client/image-component.js")},50717:(e,t,s)=>{Promise.resolve().then(s.bind(s,39130)),Promise.resolve().then(s.bind(s,45196)),Promise.resolve().then(s.t.bind(s,46533,23)),Promise.resolve().then(s.bind(s,24222)),Promise.resolve().then(s.bind(s,7485))},55453:(e,t,s)=>{Promise.resolve().then(s.bind(s,75788)),Promise.resolve().then(s.bind(s,80994)),Promise.resolve().then(s.t.bind(s,49603,23)),Promise.resolve().then(s.bind(s,42964)),Promise.resolve().then(s.bind(s,21659))},60366:(e,t,s)=>{"use strict";s.d(t,{BB:()=>a,PZ:()=>l,RI:()=>c,ut:()=>n});var r=s(64348);let i=[{slug:"text-generation",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"data-analysis",icon:"\uD83D\uDCCA",color:"#F59E0B"},{slug:"audio-processing",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"education",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"marketing",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"productivity",icon:"⚡",color:"#14B8A6"},{slug:"customer-service",icon:"\uD83C\uDFA7",color:"#F59E0B"}];async function o(e){let t=await (0,r.A)({locale:e,namespace:"categories"});return i.map(e=>({slug:e.slug,name:t(`category_names.${e.slug}`),description:t(`category_descriptions.${e.slug}`),icon:e.icon,color:e.color}))}async function a(e){return(await o(e)).map(e=>({value:e.slug,label:e.name}))}async function l(e,t){return(await o(t)).find(t=>t.slug===e)}let n=i.map(e=>e.slug),c=i.reduce((e,t)=>(e[t.slug]=t,e),{})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70099:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{default:function(){return n},getImageProps:function(){return l}});let r=s(72639),i=s(9131),o=s(49603),a=r._(s(32091));function l(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1}});for(let[e,s]of Object.entries(t))void 0===s&&delete t[e];return{props:t}}let n=o.Image},71599:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>A,generateMetadata:()=>I});var r=s(37413);s(61120);var i=s(78878),o=s(64348),a=s(42964),l=s(33488),n=s(1215),c=s(85838),d=s(21659),m=s(70099),u=s.n(m);function g({src:e,alt:t,width:s,height:i,className:o="",priority:a=!1,fill:l=!1,sizes:n,placeholder:c="empty",blurDataURL:d,fallbackSrc:m="/images/placeholder.svg"}){let g={src:e,alt:t,className:o,priority:a,placeholder:"blur"===c?"blur":"empty",blurDataURL:d||("blur"===c?"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojZjNmNGY2O3N0b3Atb3BhY2l0eToxIiAvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNlNWU3ZWI7c3RvcC1vcGFjaXR5OjEiIC8+CiAgICA8L2xpbmVhckdyYWRpZW50PgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0idXJsKCNncmFkaWVudCkiIC8+Cjwvc3ZnPg==":void 0),sizes:n||(l?"100vw":void 0)};return l?(0,r.jsx)("div",{className:"relative overflow-hidden",children:(0,r.jsx)(u(),{...g,fill:!0,style:{objectFit:"cover"}})}):(0,r.jsx)(u(),{...g,width:s,height:i})}let h={toolLogo:{width:52,height:52}},p={toolLogo:"52px"};var x=s(79171);let b=async({tool:e,onLoginRequired:t,onUnlike:s,isInLikedPage:a=!1,locale:m="en"})=>{let u=await (0,o.A)({locale:m,namespace:"common"});return(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200",style:{height:"100%"},children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[e.logo?(0,r.jsx)(g,{src:e.logo,alt:`${e.name} logo`,width:h.toolLogo.width,height:h.toolLogo.height,className:"rounded-lg object-cover",sizes:p.toolLogo,placeholder:"blur"}):(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-lg",children:e.name.charAt(0).toUpperCase()})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:e.name}),(0,r.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${(0,x.Ef)(e.pricing)}`,children:(0,x.mV)(e.pricing)})]})]}),(0,r.jsx)("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-blue-600 transition-colors",children:(0,r.jsx)(l.A,{className:"h-5 w-5"})})]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mb-4 line-clamp-2",children:e.description}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[e.tags.slice(0,3).map((e,t)=>(0,r.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:e},t)),e.tags.length>3&&(0,r.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:["+",e.tags.length-3]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(n.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:e.views})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(c.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:e.likes})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(d.default,{toolId:e._id,initialLikes:e.likes,initialLiked:a,onLoginRequired:t,onUnlike:s,isInLikedPage:a}),(0,r.jsx)(i.N_,{href:`/tools/${e._id}`,className:"inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors",children:u("view_details")})]})]})]})})};var f=s(10806),v=s(60366),j=s(54290),y=s(26373);let w=(0,y.A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);var _=s(45805);let N=(0,y.A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);async function I({params:e}){let{locale:t}=await e,s=await (0,o.A)({locale:t,namespace:"site"});try{let[e,r]=await Promise.all([f.u.getTools({status:"published",limit:1}),f.u.getCategories()]),i=e.success&&e.data?e.data.pagination.totalItems:0,o=r.success&&r.data?r.data.categories.length:0,a=s("title")+" - "+s("subtitle"),l=s("description")+s("description_with_stats",{totalTools:i,totalCategories:o}),n=s("keywords");return{title:a,description:l,keywords:n,authors:[{name:s("author")}],robots:{index:!0,follow:!0},openGraph:{type:"website",locale:"zh"===t?"zh_CN":"en_US",url:process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com",siteName:s("title"),title:a,description:l,images:[{url:"/og-homepage.jpg",width:1200,height:630,alt:a}]},twitter:{card:"summary_large_image",title:a,description:l,images:["/og-homepage.jpg"]},alternates:{canonical:process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com",languages:{zh:"/",en:"/en"}},other:{"google-site-verification":process.env.GOOGLE_SITE_VERIFICATION||""}}}catch(e){return{title:"zh"===t?"AI工具导航 - 发现最好的人工智能工具":"AI Tools Directory - Discover the Best AI Tools",description:"zh"===t?"专业的AI工具发现平台，汇集最新最好的人工智能工具。包括ChatGPT、Midjourney等热门AI工具，涵盖文本生成、图像创作、数据分析、自动化等各个领域。":"Professional AI tools discovery platform, featuring the latest and best artificial intelligence tools. Including popular tools like ChatGPT and Midjourney, covering text generation, image creation, data analysis, automation and more.",keywords:"zh"===t?"AI工具,人工智能工具,ChatGPT,Midjourney,AI工具导航,机器学习工具,深度学习,AI应用,自动化工具,智能工具,AI工具推荐":"AI tools,artificial intelligence tools,ChatGPT,Midjourney,AI tools directory,machine learning tools,deep learning,AI applications,automation tools,smart tools,AI tool recommendations"}}}async function C(e){let t=await (0,o.A)({locale:e,namespace:"home"}),s=await (0,o.A)({locale:e,namespace:"categories"});try{let[e,t,r]=await Promise.all([f.u.getTools({status:"published",limit:6,sort:"views",order:"desc"}),f.u.getTools({status:"published",limit:50,sort:"launchDate",order:"desc"}),f.u.getCategories()]),i=t.success&&t.data?t.data.tools:[],o=(r.success&&r.data?r.data.categories:[]).slice(0,4).map(e=>{let t=v.RI[e.id]||{description:"优质AI工具集合",icon:"\uD83D\uDD27",color:"#6B7280"};return{_id:e.id,name:s(`category_names.${e.id}`),slug:e.id,description:s(`category_descriptions.${e.id}`),icon:t.icon,color:t.color,toolCount:e.count}});return{featuredTools:e.success&&e.data?e.data.tools.slice(0,6):[],todayTools:i.slice(0,6),recentTools:i.slice(6,12),categories:o,error:null}}catch(e){return console.error("Failed to fetch homepage data:",e),{featuredTools:[],todayTools:[],recentTools:[],categories:[],error:t("error.network_error")}}}async function A({params:e}){let{locale:t}=await e,{featuredTools:s,todayTools:l,recentTools:n,categories:c,error:d}=await C(t),m=await (0,o.A)({locale:t,namespace:"home"}),u=(0,j.B3)(),g=(0,j.L2)(),h=[...s,...l,...n],p=h.length>0?(0,j.eb)(h):null;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(u)}}),(0,r.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(g)}}),p&&(0,r.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(p)}}),d&&(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-8",children:(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,r.jsx)("p",{className:"text-red-800",children:d})})}),(0,r.jsx)("section",{className:"bg-gradient-to-br from-blue-50 to-indigo-100 py-20",role:"banner",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("header",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-4xl md:text-6xl font-bold text-gray-900 mb-6",children:m("hero_title")}),(0,r.jsx)("p",{className:"text-xl text-gray-600 mb-8 max-w-3xl mx-auto",children:m("hero_subtitle")}),(0,r.jsx)("div",{className:"max-w-2xl mx-auto mb-8",children:(0,r.jsx)("form",{action:`${t}/search`,method:"GET",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{name:"q",type:"text",placeholder:m("search_placeholder"),className:"w-full pl-12 pr-16 py-4 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-lg"}),(0,r.jsx)(w,{className:"absolute left-4 top-4 h-6 w-6 text-gray-400"}),(0,r.jsx)("button",{type:"submit",className:"absolute right-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",style:{alignSelf:"anchor-center",whiteSpace:"nowrap",marginLeft:10,borderRadius:30,cursor:"pointer"},children:m("search_button")})]})})}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsxs)(i.N_,{href:"/tools",className:"inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(_.A,{className:"mr-2 h-5 w-5"}),m("view_all_tools")]}),(0,r.jsx)(i.N_,{href:"/submit",className:"inline-flex items-center px-8 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors",children:"zh"===t?"提交您的工具":"Submit Your Tool"})]})]})})}),(0,r.jsx)("section",{className:"py-16 bg-white","aria-labelledby":"featured-tools-heading",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"text-center mb-12",children:[(0,r.jsxs)("h2",{id:"featured-tools-heading",className:"text-3xl font-bold text-gray-900 mb-4",children:[(0,r.jsx)("span",{className:"inline-block mr-2 text-yellow-500","aria-hidden":"true",children:"⭐"}),m("featured_tools")]}),(0,r.jsx)("p",{className:"text-lg text-gray-600",children:m("featured_tools_description")})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:s.map(e=>(0,r.jsx)(b,{tool:e,locale:t},e._id))}),(0,r.jsx)("div",{className:"text-center mt-8",children:(0,r.jsx)(i.N_,{href:"/tools",className:"inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-lg text-blue-600 hover:bg-blue-50 transition-colors",children:m("view_all_tools")})})]})}),l.length>0&&(0,r.jsx)("section",{className:"py-16 bg-gradient-to-r from-green-50 to-blue-50","aria-labelledby":"today-tools-heading",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("header",{className:"text-center mb-12",children:[(0,r.jsxs)("h2",{id:"today-tools-heading",className:"text-3xl font-bold text-gray-900 mb-4",children:[(0,r.jsx)("span",{className:"inline-block mr-2 text-green-600","aria-hidden":"true",children:"\uD83D\uDCC5"}),m("today_tools")]}),(0,r.jsx)("p",{className:"text-lg text-gray-600",children:m("today_tools_description")})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",role:"list","aria-label":m("today_tools_aria_label"),children:l.map(e=>(0,r.jsx)("article",{role:"listitem",children:(0,r.jsx)(b,{tool:e,locale:t})},e._id))})]})}),n.length>0&&(0,r.jsx)("section",{className:"py-16 bg-gray-50","aria-labelledby":"recent-tools-heading",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("header",{className:"text-center mb-12",children:[(0,r.jsxs)("h2",{id:"recent-tools-heading",className:"text-3xl font-bold text-gray-900 mb-4",children:[(0,r.jsx)("span",{className:"inline-block mr-2 text-blue-600","aria-hidden":"true",children:"\uD83D\uDD52"}),m("recent_tools_title")]}),(0,r.jsx)("p",{className:"text-lg text-gray-600",children:m("recent_tools_description")})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:n.map(e=>(0,r.jsx)(b,{tool:e,locale:t},e._id))}),(0,r.jsx)("div",{className:"text-center mt-8",children:(0,r.jsx)(i.N_,{href:"/tools?sort=launchDate&order=desc",className:"inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-lg text-blue-600 hover:bg-blue-50 transition-colors",children:m("view_more_recent_tools")})})]})}),(0,r.jsx)("section",{className:"py-16 bg-white",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"text-center mb-12",children:[(0,r.jsxs)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:[(0,r.jsx)(N,{className:"inline-block mr-2 h-8 w-8 text-blue-600"}),m("popular_categories")]}),(0,r.jsx)("p",{className:"text-lg text-gray-600",children:m("popular_categories_description")})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:c.map(e=>(0,r.jsx)(a.default,{category:e},e._id))}),(0,r.jsx)("div",{className:"text-center mt-8",children:(0,r.jsx)(i.N_,{href:"/categories",className:"inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-lg text-blue-600 hover:bg-blue-50 transition-colors",children:m("view_all_categories")})})]})})]})}},79551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,4999,9658,6435,6699,8232,6533,2585,3587],()=>s(22524));module.exports=r})();