(()=>{var e={};e.id=8986,e.ids=[8986],e.modules={471:(e,t,r)=>{Promise.resolve().then(r.bind(r,75788)),Promise.resolve().then(r.bind(r,80994))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5148:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(26373).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},5199:(e,t,r)=>{Promise.resolve().then(r.bind(r,39130)),Promise.resolve().then(r.bind(r,45196))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>o});var s=r(36344),i=r(65752),a=r(13581),n=r(75745),l=r(17063);let o={...!1,providers:[(0,s.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,i.A)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET}),(0,a.A)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Code",type:"text"},token:{label:"Token",type:"text"}},async authorize(e){if(!e?.email||!e?.code||!e?.token)return null;try{await (0,n.A)();let t=await l.A.findOne({email:e.email.toLowerCase(),emailVerificationExpires:{$gt:new Date}});if(!t)return null;let r=t.emailVerificationToken;if(!r||!r.includes(":"))return null;let[s,i]=r.split(":");if(s!==e.token||i!==e.code)return null;return t.emailVerified=!0,t.emailVerificationToken=void 0,t.emailVerificationExpires=void 0,t.lastLoginAt=new Date,t.accounts.some(e=>"email"===e.provider)||t.accounts.push({provider:"email",providerId:"email",providerAccountId:t.email}),await t.save(),{id:t._id.toString(),email:t.email,name:t.name,image:t.avatar,role:t.role}}catch(e){return console.error("Email code authorization error:",e),null}}})],session:{strategy:"jwt"},callbacks:{async signIn({user:e,account:t,profile:r}){if(t?.provider==="email-code")return!0;await (0,n.A)();try{let s=await l.A.findOne({email:e.email});return s?s.lastLoginAt=new Date:s=new l.A({email:e.email,name:e.name||r?.name||"User",avatar:e.image||r?.image,emailVerified:!0,lastLoginAt:new Date}),await s.save(),t&&"email-code"!==t.provider&&(s.addAccount({provider:t.provider,providerId:t.provider,providerAccountId:t.providerAccountId||t.id||"",accessToken:t.access_token,refreshToken:t.refresh_token,expiresAt:t.expires_at?new Date(1e3*t.expires_at):void 0}),await s.save()),!0}catch(e){return console.error("Sign in error:",e),!1}},jwt:async({token:e,user:t})=>(t&&(e.userId=t.id,e.role=t.role||"user"),e),session:async({session:e,token:t})=>(t&&e.user&&(e.user.id=t.userId,e.user.role=t.role),e)},pages:{signIn:"/auth/signin",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET}},17063:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(56037),i=r.n(s);let a=new s.Schema({provider:{type:String,required:!0,enum:["google","github","email"]},providerId:{type:String,required:!0},providerAccountId:{type:String,required:!0},accessToken:String,refreshToken:String,expiresAt:Date},{_id:!1}),n=new s.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,validate:{validator:function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)},message:"Please enter a valid email address"}},name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},avatar:{type:String,trim:!0},role:{type:String,required:!0,enum:["user","admin"],default:"user"},isActive:{type:Boolean,default:!0},emailVerified:{type:Boolean,default:!1},emailVerificationToken:{type:String,trim:!0},emailVerificationExpires:{type:Date},accounts:[a],submittedTools:[{type:s.Schema.Types.ObjectId,ref:"Tool"}],likedTools:[{type:s.Schema.Types.ObjectId,ref:"Tool"}],comments:[{type:s.Schema.Types.ObjectId,ref:"Comment"}],lastLoginAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});n.index({email:1}),n.index({role:1}),n.index({emailVerificationToken:1}),n.index({"accounts.provider":1,"accounts.providerAccountId":1}),n.methods.addAccount=function(e){let t=this.accounts.find(t=>t.provider===e.provider&&t.providerAccountId===e.providerAccountId);t?Object.assign(t,e):this.accounts.push(e)},n.methods.removeAccount=function(e,t){this.accounts=this.accounts.filter(r=>r.provider!==e||r.providerAccountId!==t)};let l=i().models.User||i().model("User",n)},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26373:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var s=r(61120);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),n=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:i,className:a="",children:n,iconNode:d,...u},m)=>(0,s.createElement)("svg",{ref:m,...c,width:t,height:t,stroke:e,strokeWidth:i?24*Number(r)/Number(t):r,className:l("lucide",a),...!n&&!o(u)&&{"aria-hidden":"true"},...u},[...d.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(n)?n:[n]])),u=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...a},o)=>(0,s.createElement)(d,{ref:o,iconNode:t,className:l(`lucide-${i(n(e))}`,`lucide-${e}`,r),...a}));return r.displayName=n(e),r}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30762:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(56037),i=r.n(s),a=r(60366);let n=new s.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,maxlength:[100,"Tool name cannot exceed 100 characters"]},tagline:{type:String,trim:!0,maxlength:[200,"Tagline cannot exceed 200 characters"]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},longDescription:{type:String,trim:!0,maxlength:[2e3,"Long description cannot exceed 2000 characters"]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:a.ut},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[500,"Pricing details cannot exceed 500 characters"]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},launchDate:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});n.index({status:1,isActive:1}),n.index({category:1,status:1}),n.index({tags:1,status:1}),n.index({submittedBy:1}),n.index({launchDate:-1}),n.index({views:-1}),n.index({likes:-1}),n.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let l=i().models.Tool||i().model("Tool",n)},33873:e=>{"use strict";e.exports=require("path")},40918:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(26373).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},48094:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=r(65239),i=r(48088),a=r(88170),n=r.n(a),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let c={children:["",{children:["[locale]",{children:["submit",{children:["success",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,68369)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/success/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/success/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/[locale]/submit/success/page",pathname:"/[locale]/submit/success",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},60366:(e,t,r)=>{"use strict";r.d(t,{BB:()=>n,PZ:()=>l,RI:()=>c,ut:()=>o});var s=r(64348);let i=[{slug:"text-generation",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"data-analysis",icon:"\uD83D\uDCCA",color:"#F59E0B"},{slug:"audio-processing",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"education",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"marketing",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"productivity",icon:"⚡",color:"#14B8A6"},{slug:"customer-service",icon:"\uD83C\uDFA7",color:"#F59E0B"}];async function a(e){let t=await (0,s.A)({locale:e,namespace:"categories"});return i.map(e=>({slug:e.slug,name:t(`category_names.${e.slug}`),description:t(`category_descriptions.${e.slug}`),icon:e.icon,color:e.color}))}async function n(e){return(await a(e)).map(e=>({value:e.slug,label:e.name}))}async function l(e,t){return(await a(t)).find(t=>t.slug===e)}let o=i.map(e=>e.slug),c=i.reduce((e,t)=>(e[t.slug]=t,e),{})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68369:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var s=r(37413),i=r(78878),a=r(35426),n=r(12909),l=r(75745),o=r(30762),c=r(17063),d=r(56037),u=r.n(d),m=r(91142),p=r(5148),x=r(40918),h=r(64348);async function g({locale:e}){let t=await (0,h.A)("submit.success");return(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsx)(i.N_,{href:"/profile/submitted",className:"bg-blue-600 text-white px-6 py-3 rounded-lg font-medium text-center hover:bg-blue-700 transition-colors",children:t("view_submissions")}),(0,s.jsx)(i.N_,{href:"/",className:"bg-gray-600 text-white px-6 py-3 rounded-lg font-medium text-center hover:bg-gray-700 transition-colors",children:t("back_to_home")})]})}async function y({toolId:e,toolStatus:t,launchOption:r}){let a=await (0,h.A)("submit.success");return["pending","approved"].includes(t)?(0,s.jsx)(i.N_,{href:`/submit/launch-date/${e}?mode=edit`,className:"bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors",children:a("free"===r?"upgrade_to_paid":"edit_launch_date")}):null}function f(e,t){let r=new Date(e);return"zh"===t?r.toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric"}):r.toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}async function v(e,t){try{if(await (0,l.A)(),!u().Types.ObjectId.isValid(e))return null;let r=await c.A.findOne({email:t});if(!r)return null;let s=await o.A.findById(e);if(!s||!s.submittedBy||s.submittedBy.toString()!==r._id.toString())return null;return{_id:s._id.toString(),name:s.name,status:s.status,launchOption:s.launchOption,selectedLaunchDate:s.selectedLaunchDate?s.selectedLaunchDate.toISOString():null}}catch(e){return console.error("Failed to fetch tool:",e),null}}async function b({params:e,searchParams:t}){let r=await (0,a.getServerSession)(n.N),{locale:l}=await e,{toolId:o,paid:c}=await t,d=await (0,h.A)("submit.success");r?.user?.email||(0,i.V2)({href:"/",locale:l}),o||(0,i.V2)({href:"/",locale:l});let u="true"===c,b=await v(o,r.user.email);return b||(0,i.V2)({href:"/",locale:l}),(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("div",{className:"flex justify-center mb-4",children:(0,s.jsx)("div",{className:"bg-green-100 rounded-full p-3",children:(0,s.jsx)(m.A,{className:"h-12 w-12 text-green-600"})})}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:d(u?"payment_success_title":"submit_success_title")}),(0,s.jsx)("p",{className:"text-lg text-gray-600",children:d(u?"payment_success_desc":"submit_success_desc")})]}),(0,s.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6 mb-8",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:d("tool_info_title")}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:d("tool_name_label")}),(0,s.jsx)("span",{className:"font-medium",children:b.name})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:d("current_status_label")}),(0,s.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${"draft"===b.status?"bg-gray-100 text-gray-800":"pending"===b.status?"bg-yellow-100 text-yellow-800":"approved"===b.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:d("draft"===b.status?"status_draft":"pending"===b.status?"status_pending":"approved"===b.status?"status_approved":"status_rejected")})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:d("launch_option_label")}),(0,s.jsx)("span",{className:"font-medium",children:d("paid"===b.launchOption?"launch_option_paid":"launch_option_free")})]}),b.selectedLaunchDate&&(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:d("planned_launch_date_label")}),(0,s.jsx)("span",{className:"font-medium",children:f(b.selectedLaunchDate,l)})]})]})]}),(0,s.jsxs)("div",{className:`${"paid"===b.launchOption?"bg-blue-50 border-blue-200":"bg-gray-50 border-gray-200"} border rounded-lg p-6 mb-8`,children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("div",{className:"flex items-center",children:"paid"===b.launchOption?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p.A,{className:"h-6 w-6 text-blue-600 mr-2"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-blue-900",children:d("premium_service_title")})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(x.A,{className:"h-6 w-6 text-gray-600 mr-2"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:d("launch_date_management_title")})]})}),(0,s.jsx)(y,{toolId:b._id,toolStatus:b.status,launchOption:b.launchOption})]}),"paid"===b.launchOption&&(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-4 mb-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center text-blue-800",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2"}),(0,s.jsx)("span",{className:"text-sm",children:d("premium_features.priority_review")})]}),(0,s.jsxs)("div",{className:"flex items-center text-blue-800",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2"}),(0,s.jsx)("span",{className:"text-sm",children:d("premium_features.homepage_featured")})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center text-blue-800",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2"}),(0,s.jsx)("span",{className:"text-sm",children:d("premium_features.custom_launch_date")})]}),(0,s.jsxs)("div",{className:"flex items-center text-blue-800",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2"}),(0,s.jsx)("span",{className:"text-sm",children:d("premium_features.dedicated_support")})]})]})]}),"free"===b.launchOption&&(0,s.jsxs)("div",{className:"bg-gray-100 rounded-lg p-4",children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:d("free_service_title")}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center text-gray-700",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2"}),(0,s.jsx)("span",{className:"text-sm",children:d("free_features.standard_review")})]}),(0,s.jsxs)("div",{className:"flex items-center text-gray-700",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2"}),(0,s.jsx)("span",{className:"text-sm",children:d("free_features.flexible_date")})]}),(0,s.jsxs)("div",{className:"flex items-center text-gray-700",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2"}),(0,s.jsx)("span",{className:"text-sm",children:d("free_features.standard_position")})]})]})]}),b.selectedLaunchDate&&(0,s.jsxs)("div",{className:`bg-white rounded-lg p-4 border ${"paid"===b.launchOption?"border-blue-200":"border-gray-200"}`,children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(x.A,{className:`h-5 w-5 mr-2 ${"paid"===b.launchOption?"text-blue-600":"text-gray-600"}`}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-900",children:d("current_launch_date_label")})]}),(0,s.jsx)("span",{className:`text-sm font-semibold ${"paid"===b.launchOption?"text-blue-600":"text-gray-600"}`,children:f(b.selectedLaunchDate,l)})]}),["pending","approved"].includes(b.status)&&(0,s.jsxs)("p",{className:"text-xs text-gray-600 mt-2",children:[d("launch_date_tip"),"free"===b.launchOption&&d("launch_date_tip_free")]})]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-6 mb-8",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:d("help_title")}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:d("help_desc")}),(0,s.jsx)("div",{className:"space-y-2 text-sm text-gray-600",children:(0,s.jsx)("div",{children:d("contact_email")})})]}),(0,s.jsx)(g,{locale:l})]})}},74075:e=>{"use strict";e.exports=require("zlib")},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(56037),i=r.n(s);let a=process.env.MONGODB_URI;if(!a)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let n=global.mongoose;n||(n=global.mongoose={conn:null,promise:null});let l=async function(){if(n.conn)return n.conn;n.promise||(n.promise=i().connect(a,{bufferCommands:!1}).then(e=>e));try{n.conn=await n.promise}catch(e){throw n.promise=null,e}return n.conn}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91142:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(26373).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,4999,9658,6435,6699,8232,3136,2585],()=>r(48094));module.exports=s})();