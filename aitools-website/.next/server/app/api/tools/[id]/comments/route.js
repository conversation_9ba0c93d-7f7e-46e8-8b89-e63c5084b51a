(()=>{var e={};e.id=6792,e.ids=[6792],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3845:(e,t,r)=>{var i={"./en.json":[31960,1960],"./zh.json":[67601,7601]};function s(e){if(!r.o(i,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=i[e],s=t[0];return r.e(t[1]).then(()=>r.t(s,19))}s.keys=()=>Object.keys(i),s.id=3845,e.exports=s},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12688:(e,t,r)=>{"use strict";r.d(t,{IB:()=>i,q:()=>s});let i=["en","zh"],s="en"},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>l});var i=r(36344),s=r(65752),a=r(13581),n=r(75745),o=r(17063);let l={...!1,providers:[(0,i.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,s.A)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET}),(0,a.A)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Code",type:"text"},token:{label:"Token",type:"text"}},async authorize(e){if(!e?.email||!e?.code||!e?.token)return null;try{await (0,n.A)();let t=await o.A.findOne({email:e.email.toLowerCase(),emailVerificationExpires:{$gt:new Date}});if(!t)return null;let r=t.emailVerificationToken;if(!r||!r.includes(":"))return null;let[i,s]=r.split(":");if(i!==e.token||s!==e.code)return null;return t.emailVerified=!0,t.emailVerificationToken=void 0,t.emailVerificationExpires=void 0,t.lastLoginAt=new Date,t.accounts.some(e=>"email"===e.provider)||t.accounts.push({provider:"email",providerId:"email",providerAccountId:t.email}),await t.save(),{id:t._id.toString(),email:t.email,name:t.name,image:t.avatar,role:t.role}}catch(e){return console.error("Email code authorization error:",e),null}}})],session:{strategy:"jwt"},callbacks:{async signIn({user:e,account:t,profile:r}){if(t?.provider==="email-code")return!0;await (0,n.A)();try{let i=await o.A.findOne({email:e.email});return i?i.lastLoginAt=new Date:i=new o.A({email:e.email,name:e.name||r?.name||"User",avatar:e.image||r?.image,emailVerified:!0,lastLoginAt:new Date}),await i.save(),t&&"email-code"!==t.provider&&(i.addAccount({provider:t.provider,providerId:t.provider,providerAccountId:t.providerAccountId||t.id||"",accessToken:t.access_token,refreshToken:t.refresh_token,expiresAt:t.expires_at?new Date(1e3*t.expires_at):void 0}),await i.save()),!0}catch(e){return console.error("Sign in error:",e),!1}},jwt:async({token:e,user:t})=>(t&&(e.userId=t.id,e.role=t.role||"user"),e),session:async({session:e,token:t})=>(t&&e.user&&(e.user.id=t.userId,e.user.role=t.role),e)},pages:{signIn:"/auth/signin",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET}},17063:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var i=r(56037),s=r.n(i);let a=new i.Schema({provider:{type:String,required:!0,enum:["google","github","email"]},providerId:{type:String,required:!0},providerAccountId:{type:String,required:!0},accessToken:String,refreshToken:String,expiresAt:Date},{_id:!1}),n=new i.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,validate:{validator:function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)},message:"Please enter a valid email address"}},name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},avatar:{type:String,trim:!0},role:{type:String,required:!0,enum:["user","admin"],default:"user"},isActive:{type:Boolean,default:!0},emailVerified:{type:Boolean,default:!1},emailVerificationToken:{type:String,trim:!0},emailVerificationExpires:{type:Date},accounts:[a],submittedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],likedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],comments:[{type:i.Schema.Types.ObjectId,ref:"Comment"}],lastLoginAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});n.index({email:1}),n.index({role:1}),n.index({emailVerificationToken:1}),n.index({"accounts.provider":1,"accounts.providerAccountId":1}),n.methods.addAccount=function(e){let t=this.accounts.find(t=>t.provider===e.provider&&t.providerAccountId===e.providerAccountId);t?Object.assign(t,e):this.accounts.push(e)},n.methods.removeAccount=function(e,t){this.accounts=this.accounts.filter(r=>r.provider!==e||r.providerAccountId!==t)};let o=s().models.User||s().model("User",n)},17941:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var i=r(35471),s=r(12688);let a=(0,i.A)(async({locale:e})=>(s.IB.includes(e)||(e=s.q),{locale:e,messages:(await r(3845)(`./${e}.json`)).default}))},19854:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a.default}});var s=r(12269);Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===s[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}}))});var a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(t);if(r&&r.has(e))return r.get(e);var i={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&({}).hasOwnProperty.call(e,a)){var o=s?Object.getOwnPropertyDescriptor(e,a):null;o&&(o.get||o.set)?Object.defineProperty(i,a,o):i[a]=e[a]}return i.default=e,r&&r.set(e,i),i}(r(35426));function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===a[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return a[e]}}))})},24443:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var i=r(56037),s=r.n(i);let a=new i.Schema({toolId:{type:i.Schema.Types.ObjectId,ref:"Tool",required:[!0,"Tool ID is required"]},userId:{type:i.Schema.Types.ObjectId,ref:"User",required:[!0,"User ID is required"]},content:{type:String,required:[!0,"Comment content is required"],trim:!0,maxlength:[1e3,"Comment cannot exceed 1000 characters"],minlength:[1,"Comment cannot be empty"]},parentId:{type:i.Schema.Types.ObjectId,ref:"Comment",default:null},likes:{type:Number,default:0,min:0},isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});a.index({toolId:1,createdAt:-1}),a.index({userId:1}),a.index({parentId:1}),a.index({isActive:1}),a.virtual("replyCount",{ref:"Comment",localField:"_id",foreignField:"parentId",count:!0,match:{isActive:!0}}),a.statics.getToolComments=function(e){return this.find({toolId:e,isActive:!0}).populate("userId","name avatar").populate({path:"parentId",select:"content userId",populate:{path:"userId",select:"name"}}).sort({createdAt:-1})},a.methods.getReplies=function(){return s().model("Comment").find({parentId:this._id,isActive:!0}).populate("userId","name avatar").sort({createdAt:1})};let n=s().models.Comment||s().model("Comment",a)},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30762:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var i=r(56037),s=r.n(i),a=r(60366);let n=new i.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,maxlength:[100,"Tool name cannot exceed 100 characters"]},tagline:{type:String,trim:!0,maxlength:[200,"Tagline cannot exceed 200 characters"]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},longDescription:{type:String,trim:!0,maxlength:[2e3,"Long description cannot exceed 2000 characters"]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:a.ut},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[500,"Pricing details cannot exceed 500 characters"]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},launchDate:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});n.index({status:1,isActive:1}),n.index({category:1,status:1}),n.index({tags:1,status:1}),n.index({submittedBy:1}),n.index({launchDate:-1}),n.index({views:-1}),n.index({likes:-1}),n.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let o=s().models.Tool||s().model("Tool",n)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45196:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var i=r(78521),s=r(60687);function a({locale:e,...t}){if(!e)throw Error(void 0);return(0,s.jsx)(i.Dk,{locale:e,...t})}},46930:(e,t,r)=>{Promise.resolve().then(r.bind(r,80994))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},60366:(e,t,r)=>{"use strict";r.d(t,{BB:()=>n,PZ:()=>o,RI:()=>d,ut:()=>l});var i=r(64348);let s=[{slug:"text-generation",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"data-analysis",icon:"\uD83D\uDCCA",color:"#F59E0B"},{slug:"audio-processing",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"education",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"marketing",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"productivity",icon:"⚡",color:"#14B8A6"},{slug:"customer-service",icon:"\uD83C\uDFA7",color:"#F59E0B"}];async function a(e){let t=await (0,i.A)({locale:e,namespace:"categories"});return s.map(e=>({slug:e.slug,name:t(`category_names.${e.slug}`),description:t(`category_descriptions.${e.slug}`),icon:e.icon,color:e.color}))}async function n(e){return(await a(e)).map(e=>({value:e.slug,label:e.name}))}async function o(e,t){return(await a(t)).find(t=>t.slug===e)}let l=s.map(e=>e.slug),d=s.reduce((e,t)=>(e[t.slug]=t,e),{})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64348:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var i=r(61120),s=r(92440),a=r(84604),n=(0,i.cache)(function(e,t){return function({_cache:e=(0,a.d)(),_formatters:t=(0,a.b)(e),getMessageFallback:r=a.f,messages:i,namespace:s,onError:n=a.g,...o}){return function({messages:e,namespace:t,...r},i){return e=e["!"],t=(0,a.r)(t,"!"),(0,a.e)({...r,messages:e,namespace:t})}({...o,onError:n,cache:e,formatters:t,getMessageFallback:r,messages:{"!":i},namespace:s?`!.${s}`:"!"},"!")}({...e,namespace:t})}),o=(0,i.cache)(async function(e){let t,r;return"string"==typeof e?t=e:e&&(r=e.locale,t=e.namespace),n(await (0,s.A)(r),t)})},70490:(e,t,r)=>{Promise.resolve().then(r.bind(r,45196))},74075:e=>{"use strict";e.exports=require("zlib")},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var i=r(56037),s=r.n(i);let a=process.env.MONGODB_URI;if(!a)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let n=global.mongoose;n||(n=global.mongoose={conn:null,promise:null});let o=async function(){if(n.conn)return n.conn;n.promise||(n.promise=s().connect(a,{bufferCommands:!1}).then(e=>e));try{n.conn=await n.promise}catch(e){throw n.promise=null,e}return n.conn}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80972:(e,t,r)=>{"use strict";r.d(t,{Q$:()=>a,y3:()=>n});let i={errors:{fetch_failed:"获取数据失败",network_error:"网络错误，请重试",validation_failed:"验证失败",unauthorized:"未授权访问",forbidden:"禁止访问",not_found:"资源未找到",internal_error:"服务器内部错误",invalid_request:"无效请求",missing_required_field:"缺少必需字段",duplicate_name:"名称已存在",create_failed:"创建失败",update_failed:"更新失败",delete_failed:"删除失败"},success:{created:"创建成功",updated:"更新成功",deleted:"删除成功",submitted:"提交成功",approved:"批准成功",rejected:"拒绝成功",published:"发布成功"},tools:{fetch_failed:"获取工具列表失败",create_failed:"创建工具失败",name_required:"name 是必需的",description_required:"description 是必需的",website_required:"website 是必需的",category_required:"category 是必需的",pricing_required:"pricing 是必需的",submitter_name_required:"submitterName 是必需的",submitter_email_required:"submitterEmail 是必需的",name_exists:"该工具名称已存在",submit_success:"工具提交成功，等待审核",approve_success:"工具审核通过",reject_success:"工具已拒绝",approve_failed:"审核通过失败",reject_failed:"拒绝失败",not_found:"工具未找到",update_success:"工具更新成功",update_failed:"工具更新失败",launch_date_already_set:"此工具已经选择了发布日期",free_date_restriction:"免费选项只能选择一个月后的日期",paid_date_restriction:"付费选项最早只能选择明天的日期",launch_date_set_success:"发布日期设置成功，工具已进入审核队列",edit_not_allowed:"当前状态不允许修改发布日期",already_published:"工具已发布，无法修改发布日期",launch_date_updated:"发布日期修改成功",publish_success:"工具发布成功",publish_failed:"工具发布失败"},user:{not_found:"用户未找到",unauthorized:"用户未授权",profile_update_success:"个人资料更新成功",profile_update_failed:"个人资料更新失败"},auth:{invalid_credentials:"无效的登录凭据",code_sent:"验证码已发送",code_send_failed:"验证码发送失败",invalid_code:"无效的验证码",login_success:"登录成功",login_failed:"登录失败",logout_success:"退出成功"},payment:{create_intent_failed:"创建支付意图失败",payment_success:"支付成功",payment_failed:"支付失败",webhook_error:"Webhook 处理错误",order_created:"订单创建成功，请完成支付",upgrade_order_created:"升级订单创建成功，请完成支付"},upload:{no_file:"请选择要上传的文件",invalid_type:"只支持 JPEG、PNG、GIF、WebP 格式的图片",file_too_large:"文件大小不能超过 5MB",upload_failed:"文件上传失败",upload_success:"文件上传成功"}},s={errors:{fetch_failed:"Failed to fetch data",network_error:"Network error, please try again",validation_failed:"Validation failed",unauthorized:"Unauthorized access",forbidden:"Access forbidden",not_found:"Resource not found",internal_error:"Internal server error",invalid_request:"Invalid request",missing_required_field:"Missing required field",duplicate_name:"Name already exists",create_failed:"Creation failed",update_failed:"Update failed",delete_failed:"Deletion failed"},success:{created:"Created successfully",updated:"Updated successfully",deleted:"Deleted successfully",submitted:"Submitted successfully",approved:"Approved successfully",rejected:"Rejected successfully",published:"Published successfully"},tools:{fetch_failed:"Failed to fetch tools list",create_failed:"Failed to create tool",name_required:"name is required",description_required:"description is required",website_required:"website is required",category_required:"category is required",pricing_required:"pricing is required",submitter_name_required:"submitterName is required",submitter_email_required:"submitterEmail is required",name_exists:"Tool name already exists",submit_success:"Tool submitted successfully, awaiting review",approve_success:"Tool approved successfully",reject_success:"Tool rejected successfully",approve_failed:"Failed to approve tool",reject_failed:"Failed to reject tool",not_found:"Tool not found",update_success:"Tool updated successfully",update_failed:"Failed to update tool",launch_date_already_set:"This tool has already selected a launch date",free_date_restriction:"Free option can only select dates one month later",paid_date_restriction:"Paid option can only select dates from tomorrow",launch_date_set_success:"Launch date set successfully, tool entered review queue",edit_not_allowed:"Current status does not allow modifying launch date",already_published:"Tool already published, cannot modify launch date",launch_date_updated:"Launch date updated successfully",publish_success:"Tool published successfully",publish_failed:"Failed to publish tool"},user:{not_found:"User not found",unauthorized:"User unauthorized",profile_update_success:"Profile updated successfully",profile_update_failed:"Failed to update profile"},auth:{invalid_credentials:"Invalid credentials",code_sent:"Verification code sent",code_send_failed:"Failed to send verification code",invalid_code:"Invalid verification code",login_success:"Login successful",login_failed:"Login failed",logout_success:"Logout successful"},payment:{create_intent_failed:"Failed to create payment intent",payment_success:"Payment successful",payment_failed:"Payment failed",webhook_error:"Webhook processing error",order_created:"Order created successfully, please complete payment",upgrade_order_created:"Upgrade order created successfully, please complete payment"},upload:{no_file:"Please select a file to upload",invalid_type:"Only JPEG, PNG, GIF, WebP image formats are supported",file_too_large:"File size cannot exceed 5MB",upload_failed:"File upload failed",upload_success:"File uploaded successfully"}};function a(e,t){let r=t.split("."),a="zh"===e?i:s;for(let t of r)if(!a||"object"!=typeof a||!(t in a))return"zh"===e?"操作失败":"Operation failed";else a=a[t];return"string"==typeof a?a:"zh"===e?"操作失败":"Operation failed"}function n(e){let t=e.headers.get("x-locale");if("en"===t||"zh"===t)return t;let r=e.headers.get("accept-language")||"",i=new URL(e.url).pathname;return i.startsWith("/en/")?"en":i.startsWith("/zh/")?"zh":r.includes("en")?"en":"zh"}},80994:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/node_modules/next-intl/dist/esm/production/shared/NextIntlClientProvider.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/node_modules/next-intl/dist/esm/production/shared/NextIntlClientProvider.js","default")},81630:e=>{"use strict";e.exports=require("http")},82652:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>y,serverHooks:()=>b,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>v});var i={};r.r(i),r.d(i,{GET:()=>g,POST:()=>_});var s=r(96559),a=r(48088),n=r(37719),o=r(32190),l=r(19854),d=r(12909),c=r(75745),u=r(24443),p=r(17063),m=r(30762),f=r(80972);async function g(e,{params:t}){try{await (0,c.A)();let{id:r}=await t,{searchParams:i}=new URL(e.url),s=parseInt(i.get("page")||"1"),a=parseInt(i.get("limit")||"10"),n=(s-1)*a,l=await u.A.find({toolId:r,isActive:!0,parentId:null}).populate("userId","name email image").sort({createdAt:-1}).skip(n).limit(a),d=await Promise.all(l.map(async e=>{let t=await u.A.find({parentId:e._id,isActive:!0}).populate("userId","name email image").sort({createdAt:1});return{...e.toObject(),replies:t}})),p=await u.A.countDocuments({toolId:r,isActive:!0,parentId:{$exists:!1}});return o.NextResponse.json({success:!0,data:{comments:d,pagination:{page:s,limit:a,total:p,pages:Math.ceil(p/a)}}})}catch(r){console.error("Get comments error:",r);let t=(0,f.y3)(e);return o.NextResponse.json({success:!1,message:(0,f.Q$)(t,"errors.internal_error")},{status:500})}}async function _(e,{params:t}){try{let r=await (0,l.getServerSession)(d.N),i=(0,f.y3)(e);if(!r?.user?.email)return o.NextResponse.json({success:!1,message:(0,f.Q$)(i,"user.unauthorized")},{status:401});await (0,c.A)();let s=await p.A.findOne({email:r.user.email});if(!s)return o.NextResponse.json({success:!1,message:(0,f.Q$)(i,"user.not_found")},{status:404});let{id:a}=await t,{content:n,parentId:g}=await e.json();if(!n||0===n.trim().length||n.length>1e3)return o.NextResponse.json({success:!1,message:(0,f.Q$)(i,"errors.invalid_request")},{status:400});if(!await m.A.findById(a))return o.NextResponse.json({success:!1,message:(0,f.Q$)(i,"tools.not_found")},{status:404});if(g){let e=await u.A.findById(g);if(!e||e.toolId?.toString()!==a)return o.NextResponse.json({success:!1,message:(0,f.Q$)(i,"errors.not_found")},{status:404})}let _=new u.A({toolId:a,userId:s._id,content:n.trim(),parentId:g||void 0});return await _.save(),await _.populate("userId","name email image"),s.comments.includes(_._id.toString())||(s.comments.push(_._id.toString()),await s.save()),o.NextResponse.json({success:!0,data:_},{status:201})}catch(r){console.error("Create comment error:",r);let t=(0,f.y3)(e);return o.NextResponse.json({success:!1,message:(0,f.Q$)(t,"errors.internal_error")},{status:500})}}let y=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/tools/[id]/comments/route",pathname:"/api/tools/[id]/comments",filename:"route",bundlePath:"app/api/tools/[id]/comments/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/comments/route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:h,workUnitAsyncStorage:v,serverHooks:b}=y;function w(){return(0,n.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:v})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4243,4999,9658,580,3136],()=>r(82652));module.exports=i})();