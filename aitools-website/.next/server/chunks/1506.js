exports.id=1506,exports.ids=[1506],exports.modules={595:(e,s,t)=>{Promise.resolve().then(t.bind(t,45196)),Promise.resolve().then(t.bind(t,2639))},2639:(e,s,t)=>{"use strict";t.d(s,{default:()=>N});var r=t(60687),l=t(43210),a=t(82136),i=t(12340),o=t(77618),n=t(33823),c=t(78890),d=t(48577),m=t(94865),u=t(28559),g=t(62688);let x=(0,g.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]),h=(0,g.A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),p=(0,g.A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]),b=["ai_assistant","chatgpt","conversational_ai","smart_qa","language_model","writing_assistant","content_generation","copywriting","blog_writing","marketing_copy","image_generation","image_editing","ai_painting","avatar_generation","background_removal","video_generation","video_editing","video_clipping","short_video_creation","video_subtitles","speech_synthesis","speech_recognition","music_generation","speech_to_text","text_to_speech","code_generation","code_completion","code_review","development_assistant","low_code_platform","data_analysis","data_visualization","business_intelligence","machine_learning","deep_learning","office_automation","document_processing","project_management","team_collaboration","note_taking","ui_design","logo_design","web_design","graphic_design","prototype_design","seo_optimization","social_media_marketing","email_marketing","content_marketing","market_analysis","machine_translation","real_time_translation","document_translation","voice_translation"];var f=t(11860),y=t(99270);let j=(0,g.A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]]);function v({selectedTags:e,onTagsChange:s,maxTags:t=3,placeholder:a}){let[n,c]=(0,l.useState)(""),[d,m]=(0,l.useState)(!1),u=(0,i.a8)(),g=(0,o.c3)("common"),x=function(){let e=(0,o.c3)("tags");return b.map(s=>({key:s,label:e(s)}))}();u?.startsWith("/en");let h=r=>{e.includes(r)?s(e.filter(e=>e!==r)):e.length<t&&s([...e,r])},p=t=>{s(e.filter(e=>e!==t))},v=x.filter(s=>s.label.toLowerCase().includes(n.toLowerCase())&&!e.includes(s.key)),N=e=>{let s=x.find(s=>s.key===e);return s?s.label:e};return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:g("select_tags")}),(0,r.jsx)("span",{className:"text-sm text-gray-500",children:g("selected_count",{count:e.length,max:t})})]}),e.length>0&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-700",children:g("selected_tags")}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:e.map(e=>(0,r.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800",children:[N(e),(0,r.jsx)("button",{type:"button",onClick:()=>p(e),className:"ml-2 text-blue-600 hover:text-blue-800",children:(0,r.jsx)(f.A,{className:"h-3 w-3"})})]},e))})]}),(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:g("select_tags_max",{max:t})}),(0,r.jsxs)("div",{className:"relative mb-3",children:[(0,r.jsx)("input",{type:"text",placeholder:a||g("search_tags"),value:n,onChange:e=>c(e.target.value),onFocus:()=>m(!0),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,r.jsx)(y.A,{className:"absolute left-3 top-2.5 h-4 w-4 text-gray-400"})]}),(d||n)&&(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)("div",{className:"absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto",children:v.length>0?(0,r.jsxs)("div",{className:"p-2",children:[(0,r.jsx)("div",{className:"grid grid-cols-1 gap-1",children:v.map(s=>{let l=e.length>=t;return(0,r.jsx)("button",{type:"button",onClick:()=>{h(s.key),c(""),m(!1)},disabled:l,className:`
                              w-full px-3 py-2 text-sm text-left rounded-md transition-colors
                              ${l?"bg-gray-100 text-gray-400 cursor-not-allowed":"hover:bg-blue-50 text-gray-700"}
                            `,children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(j,{className:"h-3 w-3 mr-2 text-gray-400"}),s.label]})},s.key)})}),v.length>50&&(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-2 px-3",children:g("found_tags",{count:v.length})})]}):(0,r.jsx)("div",{className:"p-4 text-center text-gray-500 text-sm",children:g(n?"no_tags_found":"start_typing")})})})]})}),(d||n)&&(0,r.jsx)("div",{className:"fixed inset-0 z-0",onClick:()=>{m(!1),c("")}}),e.length>=t&&(0,r.jsx)("p",{className:"text-sm text-amber-600",children:g("max_tags_limit",{max:t})})]})}function N({categoryOptions:e,isEditMode:s=!1,toolId:t,initialTool:g}){let b=(0,o.c3)("submit"),{data:f,status:y}=(0,a.useSession)(),j=(0,i.rd)(),[N,w]=(0,l.useState)(g||null),[_,k]=(0,l.useState)(s&&!g),[A,E]=(0,l.useState)({name:"",tagline:"",description:"",website:"",logoFile:null,category:"",tags:[],pricing:""}),[F,C]=(0,l.useState)(null),[R,I]=(0,l.useState)(""),[U,M]=(0,l.useState)(!1),[S,P]=(0,l.useState)(!1),[T,$]=(0,l.useState)("idle"),[q,L]=(0,l.useState)(""),[D,z]=(0,l.useState)({}),[B,O]=(0,l.useState)(!1),H=e=>{let{name:s,value:t}=e.target;E(e=>({...e,[s]:t}))},Y=e=>{let s=e.target.files?.[0];if(s){E(e=>({...e,logoFile:s}));let e=new FileReader;e.onload=e=>{C(e.target?.result)},e.readAsDataURL(s)}},W=()=>{let e={};return A.name.trim()||(e.name=b("form.tool_name")+" is required"),A.description.trim()||(e.description=b("form.description")+" is required"),A.website.trim()||(e.website=b("form.website_url")+" is required"),A.category||(e.category=b("form.category")+" is required"),A.pricing||(e.pricing=b("form.pricing_model")+" is required"),A.website&&!A.website.match(/^https?:\/\/.+/)&&(e.website=b("form.website_url_placeholder")),s||A.logoFile||(e.logo=b("form.logo_required")),0===A.tags.length&&(e.tags=b("form.tags_placeholder")),z(e),0===Object.keys(e).length},Z=async e=>{if(e.preventDefault(),!f?.user?.email)return void O(!0);if(W()){P(!0),$("idle");try{let e=R;if(A.logoFile){let s=new FormData;s.append("logo",A.logoFile);let t=await fetch("/api/upload/logo",{method:"POST",body:s});if(t.ok)e=(await t.json()).data.url;else{let e=await t.json();throw Error(e.message||"Logo upload failed")}}if(s&&t){let s={name:A.name,tagline:A.tagline,description:A.description,website:A.website,logo:e||void 0,category:A.category,tags:A.tags,pricing:A.pricing},r=await fetch(`/api/tools/${t}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)}),l=await r.json();l.success?($("success"),L("工具信息更新成功！"),setTimeout(()=>{j.push("/profile/submitted")},2e3)):($("error"),L(l.error||"Update failed, please retry"))}else{let s={name:A.name,tagline:A.tagline,description:A.description,website:A.website,logoUrl:e,category:A.category,tags:A.tags,pricing:A.pricing},t=await fetch("/api/tools/submit",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(t.ok){let e=await t.json();$("success"),L(b("form.success_message")),setTimeout(()=>{j.push(`/submit/success?toolId=${e.data.toolId}`)},2e3)}else{let e=await t.json();throw Error(e.message||"Submission failed")}}}catch(e){console.error("Submit error:",e),$("error"),L(b("form.error_message"))}finally{P(!1)}}};return"loading"===y||_?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)(n.A,{size:"lg"})}):f?s&&!N?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Tool not found"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"The tool you want to edit does not exist or has been deleted."}),(0,r.jsx)(i.N_,{href:"/profile/submitted",className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:"Back to Tools"})]})}):"success"===T?(0,r.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,r.jsx)(c.A,{message:q||b("form.success_message")})}):(0,r.jsxs)(l.Fragment,{children:[(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[s&&(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)(i.N_,{href:"/profile/submitted",className:"inline-flex items-center text-blue-600 hover:text-blue-700 mb-4",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Back to Tools"]}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Edit Tool"}),(0,r.jsx)("p",{className:"text-gray-600 mt-2",children:"Update your tool information to help more users understand your product."})]}),!s&&(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"flex justify-center mb-4",children:(0,r.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,r.jsx)(x,{className:"h-8 w-8 text-blue-600"})})}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:b("title")}),(0,r.jsx)("p",{className:"text-lg text-gray-600",children:b("subtitle")})]}),"error"===T&&(0,r.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,r.jsx)("p",{className:"text-red-800",children:q})}),(0,r.jsxs)("form",{onSubmit:Z,className:"max-w-4xl mx-auto space-y-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-6 flex items-center",children:[(0,r.jsx)(h,{className:"h-5 w-5 mr-2 text-blue-600"}),b("form.basic_info")]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:[b("form.tool_name")," ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("input",{type:"text",id:"name",name:"name",value:A.name,onChange:H,placeholder:b("form.tool_name_placeholder"),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"tagline",className:"block text-sm font-medium text-gray-700 mb-2",children:[b("form.tagline")," ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("input",{type:"text",id:"tagline",name:"tagline",value:A.tagline,onChange:H,placeholder:b("form.tagline_placeholder"),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsxs)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:[b("form.description")," ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("textarea",{id:"description",name:"description",value:A.description,onChange:H,placeholder:b("form.description_placeholder"),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsxs)("label",{htmlFor:"website",className:"block text-sm font-medium text-gray-700 mb-2",children:[b("form.website_url")," ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(p,{className:"h-5 w-5 text-gray-400"})}),!s&&(0,r.jsx)("input",{type:"url",id:"website",name:"website",value:A.website,onChange:H,placeholder:s?"https://example.com":b("form.website_url_placeholder"),className:`w-full pl-10 pr-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${D.website?"border-red-300":"border-gray-300"}`,required:!0}),s&&(0,r.jsx)("div",{className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600",children:A.website})]}),D.website&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:D.website})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[b("form.logo_upload")," ",!s&&(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-6",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors",children:[(0,r.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{e.target.files?.[0]&&Y(e)},className:"hidden",id:"logo-upload",required:!s}),(0,r.jsxs)("label",{htmlFor:"logo-upload",className:"cursor-pointer",children:[(0,r.jsx)(x,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:U?b("form.uploading"):b("form.click_to_upload")}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:b("form.logo_upload_hint")})]})]}),D.logo&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:D.logo})]}),F&&(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"border border-gray-300 rounded-md overflow-hidden w-24 h-24",children:(0,r.jsx)("img",{src:F,alt:b("form.logo_preview"),className:"w-full h-full object-cover"})})})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-6",children:b("form.category_and_pricing")}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-2",children:[b("form.category")," ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("select",{id:"category",name:"category",value:A.category,onChange:H,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0,children:[(0,r.jsx)("option",{value:"",children:b("form.category_placeholder")}),e.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"pricing",className:"block text-sm font-medium text-gray-700 mb-2",children:[b("form.pricing_model")," ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("select",{id:"pricing",name:"pricing",value:A.pricing,onChange:H,className:`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${D.pricing?"border-red-300":"border-gray-300"}`,required:!0,children:[(0,r.jsx)("option",{value:"",children:b("form.pricing_placeholder")}),m.Y$.map(e=>(0,r.jsx)("option",{value:e.value,children:b(`form.${e.value}`)},e.value))]}),D.pricing&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:D.pricing})]})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[b("form.tags")," ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)(v,{selectedTags:A.tags,onTagsChange:e=>{E(s=>({...s,tags:e}))},maxTags:3,placeholder:b("form.tags_placeholder")}),D.tags&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:D.tags})]})]}),!s&&(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-4",children:b("form.guidelines_title")}),(0,r.jsxs)("ul",{className:"space-y-2 text-blue-800",children:[(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),b("form.guideline_1")]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),b("form.guideline_2")]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),b("form.guideline_3")]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),b("form.guideline_4")]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),b("form.guideline_5")]})]})]}),(0,r.jsx)("div",{className:s?"flex justify-end":"flex justify-center",children:(0,r.jsx)("button",{type:"submit",disabled:S,className:"inline-flex items-center border border-transparent font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors px-8 py-3 text-base",children:S?(0,r.jsxs)(l.Fragment,{children:[(0,r.jsx)(n.A,{size:"sm",className:"mr-2"}),b("form.submitting")]}):(0,r.jsxs)(l.Fragment,{children:[(0,r.jsx)(x,{className:"h-5 w-5 mr-2"}),b("form.submit_button")]})})})]})]}),(0,r.jsx)(d.A,{isOpen:B,onClose:()=>O(!1)})]}):(0,r.jsxs)(l.Fragment,{children:[(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:b("auth.login_required")}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:b("auth.login_to_submit")}),(0,r.jsx)("button",{onClick:()=>O(!0),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:b("auth.login")})]})}),(0,r.jsx)(d.A,{isOpen:B,onClose:()=>O(!1)})]})}},5336:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},11860:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},28559:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},33823:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});var r=t(60687);function l({size:e="md",className:s=""}){return(0,r.jsx)("div",{className:`flex justify-center items-center ${s}`,children:(0,r.jsx)("div",{className:`${{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[e]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`})})}},39636:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/submit/SubmitFormClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/submit/SubmitFormClient.tsx","default")},60366:(e,s,t)=>{"use strict";t.d(s,{BB:()=>i,PZ:()=>o,RI:()=>c,ut:()=>n});var r=t(64348);let l=[{slug:"text-generation",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"data-analysis",icon:"\uD83D\uDCCA",color:"#F59E0B"},{slug:"audio-processing",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"education",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"marketing",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"productivity",icon:"⚡",color:"#14B8A6"},{slug:"customer-service",icon:"\uD83C\uDFA7",color:"#F59E0B"}];async function a(e){let s=await (0,r.A)({locale:e,namespace:"categories"});return l.map(e=>({slug:e.slug,name:s(`category_names.${e.slug}`),description:s(`category_descriptions.${e.slug}`),icon:e.icon,color:e.color}))}async function i(e){return(await a(e)).map(e=>({value:e.slug,label:e.name}))}async function o(e,s){return(await a(s)).find(s=>s.slug===e)}let n=l.map(e=>e.slug),c=l.reduce((e,s)=>(e[s.slug]=s,e),{})},62688:(e,s,t)=>{"use strict";t.d(s,{A:()=>m});var r=t(43210);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),i=e=>{let s=a(e);return s.charAt(0).toUpperCase()+s.slice(1)},o=(...e)=>e.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim(),n=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,r.forwardRef)(({color:e="currentColor",size:s=24,strokeWidth:t=2,absoluteStrokeWidth:l,className:a="",children:i,iconNode:d,...m},u)=>(0,r.createElement)("svg",{ref:u,...c,width:s,height:s,stroke:e,strokeWidth:l?24*Number(t)/Number(s):t,className:o("lucide",a),...!i&&!n(m)&&{"aria-hidden":"true"},...m},[...d.map(([e,s])=>(0,r.createElement)(e,s)),...Array.isArray(i)?i:[i]])),m=(e,s)=>{let t=(0,r.forwardRef)(({className:t,...a},n)=>(0,r.createElement)(d,{ref:n,iconNode:s,className:o(`lucide-${l(i(e))}`,`lucide-${e}`,t),...a}));return t.displayName=i(e),t}},78890:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var r=t(60687),l=t(5336),a=t(11860);function i({message:e,onClose:s,className:t=""}){return(0,r.jsx)("div",{className:`bg-green-50 border border-green-200 rounded-lg p-4 ${t}`,children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(l.A,{className:"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-green-800 text-sm",children:e})}),s&&(0,r.jsx)("button",{onClick:s,className:"ml-3 text-green-400 hover:text-green-600 transition-colors",children:(0,r.jsx)(a.A,{className:"w-4 h-4"})})]})})}},89979:(e,s,t)=>{Promise.resolve().then(t.bind(t,80994)),Promise.resolve().then(t.bind(t,39636))},94865:(e,s,t)=>{"use strict";t.d(s,{$g:()=>d,Ef:()=>n,Y$:()=>o,kX:()=>r,mV:()=>c,tF:()=>m,v4:()=>i,vS:()=>l});let r={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},l=[{id:"free",title:"免费发布",description:r.FREE_LAUNCH.description,price:r.FREE_LAUNCH.displayPrice,features:r.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:r.PRIORITY_LAUNCH.description,price:r.PRIORITY_LAUNCH.displayPrice,features:r.PRIORITY_LAUNCH.features,recommended:!0}],a={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},i=[{value:"",label:"所有价格"},{value:a.FREE.value,label:a.FREE.label},{value:a.FREEMIUM.value,label:a.FREEMIUM.label},{value:a.PAID.value,label:a.PAID.label}],o=[{value:a.FREE.value,label:a.FREE.label},{value:a.FREEMIUM.value,label:a.FREEMIUM.label},{value:a.PAID.value,label:a.PAID.label}],n=e=>{switch(e){case a.FREE.value:return a.FREE.color;case a.FREEMIUM.value:return a.FREEMIUM.color;case a.PAID.value:return a.PAID.color;default:return"bg-gray-100 text-gray-800"}},c=e=>{switch(e){case a.FREE.value:return a.FREE.label;case a.FREEMIUM.value:return a.FREEMIUM.label;case a.PAID.value:return a.PAID.label;default:return e}},d=(e,s)=>0===e?"zh"===s?"免费":"Free":`\xa5${e}`,m=(e,s="cny")=>new Intl.NumberFormat("zh-CN",{style:"currency",currency:s.toUpperCase(),minimumFractionDigits:2}).format(e/100)},99270:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};