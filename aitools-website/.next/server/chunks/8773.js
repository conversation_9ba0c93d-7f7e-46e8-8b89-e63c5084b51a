"use strict";exports.id=8773,exports.ids=[8773],exports.modules={2952:(e,t,r)=>{r.d(t,{iu:()=>n,ng:()=>o,Ay:()=>l});var s=r(60687);r(43210);var a=r(31261),i=r.n(a);function l({src:e,alt:t,width:r,height:a,className:l="",priority:n=!1,fill:o=!1,sizes:c,placeholder:d="empty",blurDataURL:u,fallbackSrc:m="/images/placeholder.svg"}){let h={src:e,alt:t,className:l,priority:n,placeholder:"blur"===d?"blur":"empty",blurDataURL:u||("blur"===d?"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojZjNmNGY2O3N0b3Atb3BhY2l0eToxIiAvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNlNWU3ZWI7c3RvcC1vcGFjaXR5OjEiIC8+CiAgICA8L2xpbmVhckdyYWRpZW50PgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0idXJsKCNncmFkaWVudCkiIC8+Cjwvc3ZnPg==":void 0),sizes:c||(o?"100vw":void 0)};return o?(0,s.jsx)("div",{className:"relative overflow-hidden",children:(0,s.jsx)(i(),{...h,fill:!0,style:{objectFit:"cover"}})}):(0,s.jsx)(i(),{...h,width:r,height:a})}let n={avatar:{width:40,height:40},avatarLarge:{width:80,height:80},toolLogo:{width:52,height:52},toolLogoLarge:{width:128,height:128},thumbnail:{width:200,height:150},card:{width:300,height:200},hero:{width:1200,height:600}},o={avatar:"40px",toolLogo:"52px",thumbnail:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",card:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",hero:"100vw",full:"100vw"}},7485:(e,t,r)=>{r.d(t,{default:()=>c});var s=r(60687);r(43210);var a=r(12340),i=r(77618),l=r(82136),n=r(23877),o=r(2328);function c({toolId:e,initialLikes:t=0,initialLiked:r=!1,onLoginRequired:c,onUnlike:d,isInLikedPage:u=!1,showCount:m=!0,size:h="md"}){let{data:p}=(0,l.useSession)(),{getToolState:g,initializeToolState:v,toggleLike:x}=(0,o.X)(),f=(0,a.a8)(),b=(0,i.c3)("common");f?.startsWith("/en");let y=g(e),w=async()=>{if(!p)return void c?.();if(y.loading)return;let t=y.liked;await x(e,u)&&u&&t&&d&&d(e)},A=(()=>{switch(h){case"sm":return{button:"p-1.5",icon:"h-4 w-4",text:"text-sm"};case"lg":return{button:"p-3",icon:"h-6 w-6",text:"text-lg"};default:return{button:"p-2",icon:"h-5 w-5",text:"text-base"}}})();return(0,s.jsxs)("button",{onClick:w,disabled:y.loading,className:`
        ${A.button}
        inline-flex items-center space-x-1
        ${y.liked?"text-red-500 hover:text-red-600":"text-gray-400 hover:text-red-500"}
        transition-colors duration-200
        disabled:opacity-50 disabled:cursor-not-allowed
      `,title:b(y.liked?"unlike":"like"),children:[y.loading?(0,s.jsx)("div",{className:`${A.icon} animate-spin rounded-full border-2 border-gray-300 border-t-red-500`}):y.liked?(0,s.jsx)(n.Mbv,{className:A.icon}):(0,s.jsx)(n.sOK,{className:A.icon}),m&&(0,s.jsx)("span",{className:`${A.text} font-medium`,children:y.likes})]})}},10806:(e,t,r)=>{function s(){if(process.env.NEXT_PUBLIC_APP_URL)return process.env.NEXT_PUBLIC_APP_URL;{if(process.env.VERCEL_URL)return`https://${process.env.VERCEL_URL}`;if(process.env.NETLIFY&&process.env.URL)return process.env.URL;if(process.env.RAILWAY_STATIC_URL)return process.env.RAILWAY_STATIC_URL;if(process.env.APP_URL)return process.env.APP_URL;let e=process.env.PORT||"3001";return`http://localhost:${e}`}}function a(){if(process.env.NEXT_PUBLIC_API_BASE_URL)return process.env.NEXT_PUBLIC_API_BASE_URL;let e=s();return`${e}/api`}function i(){return"production"}function l(){return"development"===i()}r.d(t,{u:()=>d});let n={baseUrl:s(),apiBaseUrl:a(),nextAuthUrl:process.env.NEXTAUTH_URL?process.env.NEXTAUTH_URL:s(),environment:i(),isDevelopment:l(),isProduction:"production"===i(),port:process.env.PORT||"3001"};l()&&(console.log("\uD83D\uDD27 Dynamic Environment Configuration:"),console.log("  Base URL:",n.baseUrl),console.log("  API Base URL:",n.apiBaseUrl),console.log("  NextAuth URL:",n.nextAuthUrl),console.log("  Environment:",n.environment),console.log("  Port:",n.port));let o=a();class c{constructor(e=o){this.baseURL=e}async request(e,t={}){try{let r=`${this.baseURL}${e}`,s={headers:{"Content-Type":"application/json",...t.headers},...t},a=await fetch(r,s),i=await a.json();if(!a.ok)throw Error(i.error||`HTTP error! status: ${a.status}`);return i}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,r])=>{void 0!==r&&t.append(e,r.toString())});let r=t.toString();return this.request(`/tools${r?`?${r}`:""}`)}async getTool(e){return this.request(`/tools/${e}`)}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,t){return this.request(`/tools/${e}`,{method:"PUT",body:JSON.stringify(t)})}async deleteTool(e){return this.request(`/tools/${e}`,{method:"DELETE"})}async getLikedTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,r])=>{void 0!==r&&t.append(e,r.toString())});let r=t.toString();return this.request(`/user/liked-tools${r?`?${r}`:""}`)}async getAdminTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,r])=>{void 0!==r&&t.append(e,r.toString())});let r=t.toString();return this.request(`/admin/tools${r?`?${r}`:""}`)}async approveTool(e,t){return this.request(`/admin/tools/${e}/approve`,{method:"POST",body:JSON.stringify(t)})}async rejectTool(e,t){return this.request(`/admin/tools/${e}/reject`,{method:"POST",body:JSON.stringify(t)})}async getAdminStats(e){let t=e?`?timeRange=${e}`:"";return this.request(`/admin/stats${t}`)}async getCategories(){return this.request("/categories")}async getOrder(e){return this.request(`/orders/${e}`)}async processOrderPayment(e,t){return this.request(`/orders/${e}/pay`,{method:"POST",body:JSON.stringify(t)})}}let d=new c},13861:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},25334:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},31261:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return o},getImageProps:function(){return n}});let s=r(14985),a=r(44953),i=r(46533),l=s._(r(1933));function n(e){let{props:t}=(0,a.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let o=i.Image},62688:(e,t,r)=>{r.d(t,{A:()=>u});var s=r(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),l=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:i="",children:l,iconNode:d,...u},m)=>(0,s.createElement)("svg",{ref:m,...c,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:n("lucide",i),...!l&&!o(u)&&{"aria-hidden":"true"},...u},[...d.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(l)?l:[l]])),u=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...i},o)=>(0,s.createElement)(d,{ref:o,iconNode:t,className:n(`lucide-${a(l(e))}`,`lucide-${e}`,r),...i}));return r.displayName=l(e),r}},67760:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},74820:(e,t,r)=>{r.d(t,{A:()=>h});var s=r(60687);r(43210);var a=r(12340);function i(e){return()=>{throw Error(`\`${e}\` is not supported in Client Components.`)}}i("getFormatter"),i("getNow"),i("getTimeZone"),i("getMessages"),i("getLocale");let l=i("getTranslations");i("setRequestLocale");var n=r(25334),o=r(13861),c=r(67760),d=r(7485),u=r(2952),m=r(94865);let h=async({tool:e,onLoginRequired:t,onUnlike:r,isInLikedPage:i=!1,locale:h="en"})=>{let p=await l({locale:h,namespace:"common"});return(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200",style:{height:"100%"},children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[e.logo?(0,s.jsx)(u.Ay,{src:e.logo,alt:`${e.name} logo`,width:u.iu.toolLogo.width,height:u.iu.toolLogo.height,className:"rounded-lg object-cover",sizes:u.ng.toolLogo,placeholder:"blur"}):(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white font-bold text-lg",children:e.name.charAt(0).toUpperCase()})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:e.name}),(0,s.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${(0,m.Ef)(e.pricing)}`,children:(0,m.mV)(e.pricing)})]})]}),(0,s.jsx)("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-blue-600 transition-colors",children:(0,s.jsx)(n.A,{className:"h-5 w-5"})})]}),(0,s.jsx)("p",{className:"text-gray-600 text-sm mb-4 line-clamp-2",children:e.description}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[e.tags.slice(0,3).map((e,t)=>(0,s.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:e},t)),e.tags.length>3&&(0,s.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:["+",e.tags.length-3]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,s.jsx)(o.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:e.views})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,s.jsx)(c.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:e.likes})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(d.default,{toolId:e._id,initialLikes:e.likes,initialLiked:i,onLoginRequired:t,onUnlike:r,isInLikedPage:i}),(0,s.jsx)(a.N_,{href:`/tools/${e._id}`,className:"inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors",children:p("view_details")})]})]})]})})}},94865:(e,t,r)=>{r.d(t,{$g:()=>d,Ef:()=>o,Y$:()=>n,kX:()=>s,mV:()=>c,tF:()=>u,v4:()=>l,vS:()=>a});let s={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},a=[{id:"free",title:"免费发布",description:s.FREE_LAUNCH.description,price:s.FREE_LAUNCH.displayPrice,features:s.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:s.PRIORITY_LAUNCH.description,price:s.PRIORITY_LAUNCH.displayPrice,features:s.PRIORITY_LAUNCH.features,recommended:!0}],i={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},l=[{value:"",label:"所有价格"},{value:i.FREE.value,label:i.FREE.label},{value:i.FREEMIUM.value,label:i.FREEMIUM.label},{value:i.PAID.value,label:i.PAID.label}],n=[{value:i.FREE.value,label:i.FREE.label},{value:i.FREEMIUM.value,label:i.FREEMIUM.label},{value:i.PAID.value,label:i.PAID.label}],o=e=>{switch(e){case i.FREE.value:return i.FREE.color;case i.FREEMIUM.value:return i.FREEMIUM.color;case i.PAID.value:return i.PAID.color;default:return"bg-gray-100 text-gray-800"}},c=e=>{switch(e){case i.FREE.value:return i.FREE.label;case i.FREEMIUM.value:return i.FREEMIUM.label;case i.PAID.value:return i.PAID.label;default:return e}},d=(e,t)=>0===e?"zh"===t?"免费":"Free":`\xa5${e}`,u=(e,t="cny")=>new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)}};